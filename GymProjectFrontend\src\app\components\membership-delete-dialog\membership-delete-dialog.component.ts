import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberActiveMemberships, ActiveMembershipDetail } from '../../models/memberActiveMemberships';

@Component({
  selector: 'app-membership-delete-dialog',
  templateUrl: './membership-delete-dialog.component.html',
  styleUrls: ['./membership-delete-dialog.component.css'],
  standalone: false
})
export class MembershipDeleteDialogComponent implements OnInit {
  memberActiveMemberships: MemberActiveMemberships | null = null;
  selectedMembershipId: number | null = null;
  isLoading = true;

  constructor(
    public dialogRef: MatDialogRef<MembershipDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { memberId: number, memberName: string },
    private membershipService: MembershipService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadMemberActiveMemberships();
  }

  loadMemberActiveMemberships(): void {
    this.membershipService.getMemberActiveMemberships(this.data.memberId).subscribe({
      next: (response) => {
        if (response.success) {
          this.memberActiveMemberships = response.data;
          
          // Eğer sadece bir aktif üyelik varsa otomatik seç
          if (this.memberActiveMemberships.activeMemberships.length === 1) {
            this.selectedMembershipId = this.memberActiveMemberships.activeMemberships[0].membershipID;
          }
        } else {
          this.toastr.error('Aktif üyelikler yüklenemedi', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Aktif üyelikler yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  selectMembership(membershipId: number): void {
    this.selectedMembershipId = membershipId;
  }

  getSelectedMembership(): ActiveMembershipDetail | null {
    if (!this.memberActiveMemberships || !this.selectedMembershipId) {
      return null;
    }
    return this.memberActiveMemberships.activeMemberships.find(
      m => m.membershipID === this.selectedMembershipId
    ) || null;
  }

  onConfirm(): void {
    if (!this.selectedMembershipId) {
      this.toastr.warning('Lütfen silinecek üyeliği seçin', 'Uyarı');
      return;
    }

    this.dialogRef.close({ confirmed: true, membershipId: this.selectedMembershipId });
  }

  onCancel(): void {
    this.dialogRef.close({ confirmed: false });
  }

  formatCurrency(amount: number): string {
    return amount.toLocaleString('tr-TR', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    }) + '₺';
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('tr-TR');
  }
}
