import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { BaseApiService } from './baseApiService';

@Injectable({
  providedIn: 'root'
})
export class RateLimitService extends BaseApiService {

  constructor(private httpClient: HttpClient) {
    super();
  }

  checkLoginBan(deviceInfo: string): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}auth/check-login-ban?deviceInfo=${encodeURIComponent(deviceInfo)}`);
  }

  checkRegisterBan(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}auth/check-register-ban`);
  }

  getRemainingProfileImageUploads(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}user/remaining-profile-image-uploads`);
  }

  getRemainingFileDownloads(): Observable<any> {
    return this.httpClient.get<any>(`${this.apiUrl}user/remaining-file-downloads`);
  }

  recordFileDownload(): Observable<any> {
    return this.httpClient.post<any>(`${this.apiUrl}user/record-file-download`, {});
  }

  formatRemainingTime(minutes: number): string {
    if (minutes <= 0) return '';

    if (minutes >= 1440) { // 24 saat = 1440 dakika
      const days = Math.floor(minutes / 1440);
      const remainingHours = Math.floor((minutes % 1440) / 60);
      if (remainingHours > 0) {
        return `${days} gün ${remainingHours} saat`;
      }
      return `${days} gün`;
    } else if (minutes >= 60) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (remainingMinutes > 0) {
        return `${hours} saat ${remainingMinutes} dakika`;
      }
      return `${hours} saat`;
    } else {
      return `${minutes} dakika`;
    }
  }
}
