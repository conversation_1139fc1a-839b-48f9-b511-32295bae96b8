/// Auth Models - GymKod Pro Mobile
/// 
/// Bu modeller backend auth API'lerinden uyarlanmıştır.
/// Referans: GymProjectBackend/WebAPI/Controllers/AuthController.cs
library;

import 'package:json_annotation/json_annotation.dart';

part 'auth_models.g.dart';

/// Login Request Model (Backend: LoginRequestModel)
@JsonSerializable()
class LoginRequest {
  final LoginDto loginDto;
  final String deviceInfo;

  const LoginRequest({
    required this.loginDto,
    required this.deviceInfo,
  });

  factory LoginRequest.fromJson(Map<String, dynamic> json) =>
      _$LoginRequestFromJson(json);

  Map<String, dynamic> toJson() => _$LoginRequestToJson(this);
}

/// Login DTO (Backend: UserForLoginDto)
@JsonSerializable()
class LoginDto {
  final String email;
  final String password;

  const LoginDto({
    required this.email,
    required this.password,
  });

  factory LoginDto.fromJson(Map<String, dynamic> json) =>
      _$LoginDtoFromJson(json);

  Map<String, dynamic> toJson() => _$LoginDtoToJson(this);
}

/// Register Request Model (Backend: RegisterRequestModel)
@JsonSerializable()
class RegisterRequest {
  final RegisterDto registerDto;
  final String deviceInfo;

  const RegisterRequest({
    required this.registerDto,
    required this.deviceInfo,
  });

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$RegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterRequestToJson(this);
}

/// Register DTO (Backend: UserForRegisterDto)
@JsonSerializable()
class RegisterDto {
  final String firstName;
  final String lastName;
  final String email;
  final String password;

  const RegisterDto({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
  });

  factory RegisterDto.fromJson(Map<String, dynamic> json) =>
      _$RegisterDtoFromJson(json);

  Map<String, dynamic> toJson() => _$RegisterDtoToJson(this);
}

/// Member Register Request Model (Backend: MemberRegisterRequestModel)
@JsonSerializable()
class MemberRegisterRequest {
  final MemberRegisterDto registerDto;
  final String deviceInfo;

  const MemberRegisterRequest({
    required this.registerDto,
    required this.deviceInfo,
  });

  factory MemberRegisterRequest.fromJson(Map<String, dynamic> json) =>
      _$MemberRegisterRequestFromJson(json);

  Map<String, dynamic> toJson() => _$MemberRegisterRequestToJson(this);
}

/// Member Register DTO (Backend: MemberForRegisterDto)
@JsonSerializable()
class MemberRegisterDto {
  final String firstName;
  final String lastName;
  final String email;
  final String password;
  final String phoneNumber;

  const MemberRegisterDto({
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.password,
    required this.phoneNumber,
  });

  factory MemberRegisterDto.fromJson(Map<String, dynamic> json) =>
      _$MemberRegisterDtoFromJson(json);

  Map<String, dynamic> toJson() => _$MemberRegisterDtoToJson(this);
}

/// Refresh Token Request (Backend: RefreshTokenRequest)
@JsonSerializable()
class RefreshTokenRequest {
  final String refreshToken;
  final String deviceInfo;

  const RefreshTokenRequest({
    required this.refreshToken,
    required this.deviceInfo,
  });

  factory RefreshTokenRequest.fromJson(Map<String, dynamic> json) =>
      _$RefreshTokenRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RefreshTokenRequestToJson(this);
}

/// Auth Response Model (Backend auth controller response)
@JsonSerializable()
class AuthResponse {
  final bool success;
  final String message;
  final bool? requirePasswordChange;
  final AuthData? data;

  const AuthResponse({
    required this.success,
    required this.message,
    this.requirePasswordChange,
    this.data,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) =>
      _$AuthResponseFromJson(json);

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);
}

/// Auth Data (Backend auth response data)
@JsonSerializable()
class AuthData {
  final String token;
  final String refreshToken;
  final DateTime expiration;

  const AuthData({
    required this.token,
    required this.refreshToken,
    required this.expiration,
  });

  factory AuthData.fromJson(Map<String, dynamic> json) =>
      _$AuthDataFromJson(json);

  Map<String, dynamic> toJson() => _$AuthDataToJson(this);
}

/// Change Password Request (Backend: ChangePasswordRequest)
@JsonSerializable()
class ChangePasswordRequest {
  final String currentPassword;
  final String newPassword;

  const ChangePasswordRequest({
    required this.currentPassword,
    required this.newPassword,
  });

  factory ChangePasswordRequest.fromJson(Map<String, dynamic> json) =>
      _$ChangePasswordRequestFromJson(json);

  Map<String, dynamic> toJson() => _$ChangePasswordRequestToJson(this);
}
