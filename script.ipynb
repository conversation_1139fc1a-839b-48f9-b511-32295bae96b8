﻿{"metadata":{"kernel_spec":{"name":"SQL","language":"sql","display_name":"SQL"},"language_info":{"name":"sql","version":""}},"nbformat":4,"nbformat_minor":2,"cells":[{"cell_type":"markdown","source":["# [GymProject]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["USE [master]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Database [GymProject]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE DATABASE [GymProject]\r\n CONTAINMENT = NONE\r\n ON  PRIMARY \r\n( NAME = N'GymProject', FILENAME = N'C:\\Program Files\\Microsoft SQL Server\\MSSQL16.MSSQLSERVER\\MSSQL\\DATA\\GymProject.mdf' , SIZE = 73728KB , MAXSIZE = UNLIMITED, FILEGROWTH = 65536KB )\r\n LOG ON \r\n( NAME = N'GymProject_log', FILENAME = N'C:\\Program Files\\Microsoft SQL Server\\MSSQL16.MSSQLSERVER\\MSSQL\\DATA\\GymProject_log.ldf' , SIZE = 73728KB , MAXSIZE = 2048GB , FILEGROWTH = 65536KB )\r\n WITH CATALOG_COLLATION = DATABASE_DEFAULT, LEDGER = OFF\r\n","GO\r\n","ALTER DATABASE [GymProject] SET COMPATIBILITY_LEVEL = 160\r\n","GO\r\n","IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))\r\nbegin\r\nEXEC [GymProject].[dbo].[sp_fulltext_database] @action = 'enable'\r\nend\r\n","GO\r\n","ALTER DATABASE [GymProject] SET ANSI_NULL_DEFAULT OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ANSI_NULLS OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ANSI_PADDING OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ANSI_WARNINGS OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ARITHABORT OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET AUTO_CLOSE OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET AUTO_SHRINK OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET AUTO_UPDATE_STATISTICS ON \r\n","GO\r\n","ALTER DATABASE [GymProject] SET CURSOR_CLOSE_ON_COMMIT OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET CURSOR_DEFAULT  GLOBAL \r\n","GO\r\n","ALTER DATABASE [GymProject] SET CONCAT_NULL_YIELDS_NULL OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET NUMERIC_ROUNDABORT OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET QUOTED_IDENTIFIER OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET RECURSIVE_TRIGGERS OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET  DISABLE_BROKER \r\n","GO\r\n","ALTER DATABASE [GymProject] SET AUTO_UPDATE_STATISTICS_ASYNC OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET DATE_CORRELATION_OPTIMIZATION OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET TRUSTWORTHY OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ALLOW_SNAPSHOT_ISOLATION OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET PARAMETERIZATION SIMPLE \r\n","GO\r\n","ALTER DATABASE [GymProject] SET READ_COMMITTED_SNAPSHOT OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET HONOR_BROKER_PRIORITY OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET RECOVERY SIMPLE \r\n","GO\r\n","ALTER DATABASE [GymProject] SET  MULTI_USER \r\n","GO\r\n","ALTER DATABASE [GymProject] SET PAGE_VERIFY CHECKSUM  \r\n","GO\r\n","ALTER DATABASE [GymProject] SET DB_CHAINING OFF \r\n","GO\r\n","ALTER DATABASE [GymProject] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF ) \r\n","GO\r\n","ALTER DATABASE [GymProject] SET TARGET_RECOVERY_TIME = 60 SECONDS \r\n","GO\r\n","ALTER DATABASE [GymProject] SET DELAYED_DURABILITY = DISABLED \r\n","GO\r\n","ALTER DATABASE [GymProject] SET ACCELERATED_DATABASE_RECOVERY = OFF  \r\n","GO\r\n","ALTER DATABASE [GymProject] SET QUERY_STORE = ON\r\n","GO\r\n","ALTER DATABASE [GymProject] SET QUERY_STORE (OPERATION_MODE = READ_WRITE, CLEANUP_POLICY = (STALE_QUERY_THRESHOLD_DAYS = 30), DATA_FLUSH_INTERVAL_SECONDS = 900, INTERVAL_LENGTH_MINUTES = 60, MAX_STORAGE_SIZE_MB = 1000, QUERY_CAPTURE_MODE = AUTO, SIZE_BASED_CLEANUP_MODE = AUTO, MAX_PLANS_PER_QUERY = 200, WAIT_STATS_CAPTURE_MODE = ON)\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}},{"cell_type":"markdown","source":["# [dbo].[WorkoutProgramTemplates]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["USE [GymProject]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[WorkoutProgramTemplates]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[WorkoutProgramTemplates](\r\n\t[WorkoutProgramTemplateID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[ProgramName] [nvarchar](200) NOT NULL,\r\n\t[Description] [nvarchar](1000) NULL,\r\n\t[ExperienceLevel] [nvarchar](50) NULL,\r\n\t[TargetGoal] [nvarchar](100) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_WorkoutProgramTemplates] PRIMARY KEY CLUSTERED \r\n(\r\n\t[WorkoutProgramTemplateID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[MemberWorkoutPrograms]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[MemberWorkoutPrograms]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[MemberWorkoutPrograms](\r\n\t[MemberWorkoutProgramID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MemberID] [int] NOT NULL,\r\n\t[WorkoutProgramTemplateID] [int] NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[StartDate] [datetime2](7) NOT NULL,\r\n\t[EndDate] [datetime2](7) NULL,\r\n\t[Notes] [nvarchar](1000) NULL,\r\n\t[IsActive] [bit] NOT NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_MemberWorkoutPrograms] PRIMARY KEY CLUSTERED \r\n(\r\n\t[MemberWorkoutProgramID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Members]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Members]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Members](\r\n\t[MemberID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Name] [nvarchar](50) NOT NULL,\r\n\t[ScanNumber] [varchar](50) NULL,\r\n\t[BirthDate] [date] NULL,\r\n\t[Gender] [tinyint] NOT NULL,\r\n\t[Adress] [varchar](100) NULL,\r\n\t[PhoneNumber] [varchar](15) NOT NULL,\r\n\t[Email] [varchar](100) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[Balance] [decimal](10, 2) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[UserID] [int] NULL,\r\n CONSTRAINT [PK_Members] PRIMARY KEY CLUSTERED \r\n(\r\n\t[MemberID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[vw_MemberWorkoutProgramDetails]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']","object_type":"View"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  View [dbo].[vw_MemberWorkoutProgramDetails]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\n-- 1. ANA DETAY VIEW (Schema bound - indexlenebilir)\n-- Sık kullanılan join'ler için cache optimizasyonu\nCREATE VIEW [dbo].[vw_MemberWorkoutProgramDetails]\nWITH SCHEMABINDING\nAS\nSELECT\n    mwp.MemberWorkoutProgramID,\n    mwp.MemberID,\n    m.Name AS MemberName,\n    m.PhoneNumber AS MemberPhone,\n    m.Email AS MemberEmail,\n    mwp.WorkoutProgramTemplateID,\n    wpt.ProgramName,\n    wpt.Description AS ProgramDescription,\n    wpt.ExperienceLevel,\n    wpt.TargetGoal,\n    mwp.CompanyID,\n    mwp.StartDate,\n    mwp.EndDate,\n    mwp.Notes,\n    mwp.IsActive,\n    mwp.CreationDate,\n    mwp.UpdatedDate\nFROM dbo.MemberWorkoutPrograms mwp\nINNER JOIN dbo.Members m ON mwp.MemberID = m.MemberID\nINNER JOIN dbo.WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID\nWHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1\n\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']","object_type":"View"}},{"cell_type":"markdown","source":["# [IX_vw_MemberWorkoutProgramDetails_Clustered]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_Clustered']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ARITHABORT ON\r\nSET CONCAT_NULL_YIELDS_NULL ON\r\nSET QUOTED_IDENTIFIER ON\r\nSET ANSI_NULLS ON\r\nSET ANSI_PADDING ON\r\nSET ANSI_WARNINGS ON\r\nSET NUMERIC_ROUNDABORT OFF\r\nGO\r\n","/****** Object:  Index [IX_vw_MemberWorkoutProgramDetails_Clustered]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE UNIQUE CLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_Clustered] ON [dbo].[vw_MemberWorkoutProgramDetails]\r\n(\r\n\t[MemberWorkoutProgramID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_Clustered']","object_type":"Index"}},{"cell_type":"markdown","source":["# [dbo].[WorkoutProgramDays]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[WorkoutProgramDays]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[WorkoutProgramDays](\r\n\t[WorkoutProgramDayID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[WorkoutProgramTemplateID] [int] NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[DayNumber] [int] NOT NULL,\r\n\t[DayName] [nvarchar](100) NOT NULL,\r\n\t[IsRestDay] [bit] NOT NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_WorkoutProgramDays] PRIMARY KEY CLUSTERED \r\n(\r\n\t[WorkoutProgramDayID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[WorkoutProgramExercises]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[WorkoutProgramExercises]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[WorkoutProgramExercises](\r\n\t[WorkoutProgramExerciseID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[WorkoutProgramDayID] [int] NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[ExerciseType] [nvarchar](20) NOT NULL,\r\n\t[ExerciseID] [int] NOT NULL,\r\n\t[OrderIndex] [int] NOT NULL,\r\n\t[Sets] [int] NOT NULL,\r\n\t[Reps] [nvarchar](50) NOT NULL,\r\n\t[RestTime] [int] NULL,\r\n\t[Notes] [nvarchar](500) NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_WorkoutProgramExercises] PRIMARY KEY CLUSTERED \r\n(\r\n\t[WorkoutProgramExerciseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[vw_MemberWorkoutProgramStats]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramStats' and @Schema='dbo']","object_type":"View"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  View [dbo].[vw_MemberWorkoutProgramStats]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\n-- 3. İSTATİSTİK VIEW (Raporlama ve dashboard için)\nCREATE VIEW [dbo].[vw_MemberWorkoutProgramStats] AS\nSELECT\n    mwp.MemberWorkoutProgramID,\n    mwp.MemberID,\n    mwp.WorkoutProgramTemplateID,\n    mwp.CompanyID,\n    mwp.StartDate,\n    mwp.EndDate,\n    mwp.CreationDate,\n    -- Program gün sayısı\n    (SELECT COUNT(*) FROM WorkoutProgramDays wpd\n     WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS DayCount,\n    -- Program egzersiz sayısı\n    (SELECT COUNT(*) FROM WorkoutProgramExercises wpe\n     INNER JOIN WorkoutProgramDays wpd ON wpe.WorkoutProgramDayID = wpd.WorkoutProgramDayID\n     WHERE wpd.WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID) AS ExerciseCount,\n    -- Aktif program sayısı (üye bazında)\n    (SELECT COUNT(*) FROM MemberWorkoutPrograms mwp2\n     WHERE mwp2.MemberID = mwp.MemberID AND mwp2.IsActive = 1) AS ActiveProgramCount,\n    -- Program süresi (gün olarak)\n    CASE\n        WHEN mwp.EndDate IS NOT NULL THEN DATEDIFF(DAY, mwp.StartDate, mwp.EndDate)\n        ELSE DATEDIFF(DAY, mwp.StartDate, GETDATE())\n    END AS ProgramDurationDays\nFROM MemberWorkoutPrograms mwp\nWHERE mwp.IsActive = 1\n\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramStats' and @Schema='dbo']","object_type":"View"}},{"cell_type":"markdown","source":["# [dbo].[vw_MemberWorkoutProgramSummary]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramSummary' and @Schema='dbo']","object_type":"View"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  View [dbo].[vw_MemberWorkoutProgramSummary]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\n-- 4. ÖZET VIEW (Mobil API için hafif versiyon)\nCREATE VIEW [dbo].[vw_MemberWorkoutProgramSummary] AS\nSELECT\n    mwp.MemberWorkoutProgramID,\n    mwp.MemberID,\n    m.Name AS MemberName,\n    mwp.WorkoutProgramTemplateID,\n    wpt.ProgramName,\n    wpt.ExperienceLevel,\n    wpt.TargetGoal,\n    mwp.CompanyID,\n    mwp.StartDate,\n    mwp.EndDate,\n    mwp.IsActive,\n    -- Basit hesaplamalar\n    CASE\n        WHEN mwp.EndDate IS NOT NULL AND mwp.EndDate < GETDATE() THEN 'Tamamlandı'\n        WHEN mwp.StartDate > GETDATE() THEN 'Başlamadı'\n        ELSE 'Devam Ediyor'\n    END AS ProgramStatus\nFROM MemberWorkoutPrograms mwp\nINNER JOIN Members m ON mwp.MemberID = m.MemberID\nINNER JOIN WorkoutProgramTemplates wpt ON mwp.WorkoutProgramTemplateID = wpt.WorkoutProgramTemplateID\nWHERE mwp.IsActive = 1 AND m.IsActive = 1 AND wpt.IsActive = 1\n\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramSummary' and @Schema='dbo']","object_type":"View"}},{"cell_type":"markdown","source":["# [dbo].[Cities]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Cities' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Cities]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Cities](\r\n\t[CityID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CityName] [varchar](50) NOT NULL,\r\n CONSTRAINT [PK_Cities] PRIMARY KEY CLUSTERED \r\n(\r\n\t[CityID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Cities' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Companies' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Companies]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Companies](\r\n\t[CompanyID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CompanyName] [varchar](50) NOT NULL,\r\n\t[PhoneNumber] [varchar](15) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_Companies] PRIMARY KEY CLUSTERED \r\n(\r\n\t[CompanyID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Companies' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[CompanyAdresses]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[CompanyAdresses]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[CompanyAdresses](\r\n\t[CompanyAdressID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[CityID] [int] NOT NULL,\r\n\t[TownID] [int] NOT NULL,\r\n\t[Adress] [varchar](50) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_CompanyAdresses] PRIMARY KEY CLUSTERED \r\n(\r\n\t[CompanyAdressID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[CompanyExercises]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[CompanyExercises]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[CompanyExercises](\r\n\t[CompanyExerciseID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[ExerciseCategoryID] [int] NOT NULL,\r\n\t[ExerciseName] [nvarchar](200) NOT NULL,\r\n\t[Description] [nvarchar](1000) NULL,\r\n\t[Instructions] [nvarchar](2000) NULL,\r\n\t[MuscleGroups] [nvarchar](500) NULL,\r\n\t[Equipment] [nvarchar](200) NULL,\r\n\t[DifficultyLevel] [tinyint] NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_CompanyExercises] PRIMARY KEY CLUSTERED \r\n(\r\n\t[CompanyExerciseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[CompanyUsers]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[CompanyUsers]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[CompanyUsers](\r\n\t[CompanyUserID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CityID] [int] NOT NULL,\r\n\t[TownID] [int] NOT NULL,\r\n\t[Name] [varchar](50) NOT NULL,\r\n\t[PhoneNumber] [varchar](15) NOT NULL,\r\n\t[Email] [varchar](100) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_CompanyUsers] PRIMARY KEY CLUSTERED \r\n(\r\n\t[CompanyUserID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[DebtPayments]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[DebtPayments]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[DebtPayments](\r\n\t[DebtPaymentID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[RemainingDebtID] [int] NOT NULL,\r\n\t[PaidAmount] [decimal](18, 2) NOT NULL,\r\n\t[PaymentMethod] [nvarchar](50) NOT NULL,\r\n\t[PaymentDate] [datetime] NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[DebtPaymentID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[EntryExitHistories]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[EntryExitHistories]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[EntryExitHistories](\r\n\t[EntryExitID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MembershipID] [int] NOT NULL,\r\n\t[EntryDate] [datetime2](7) NULL,\r\n\t[ExitDate] [datetime2](7) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n CONSTRAINT [PK_EntryExitHistories] PRIMARY KEY CLUSTERED \r\n(\r\n\t[EntryExitID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[ExerciseCategories]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[ExerciseCategories]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[ExerciseCategories](\r\n\t[ExerciseCategoryID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CategoryName] [nvarchar](100) NOT NULL,\r\n\t[Description] [nvarchar](500) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_ExerciseCategories] PRIMARY KEY CLUSTERED \r\n(\r\n\t[ExerciseCategoryID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Expenses]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Expenses]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Expenses](\r\n\t[ExpenseID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[Description] [nvarchar](500) NULL,\r\n\t[Amount] [decimal](18, 2) NOT NULL,\r\n\t[ExpenseDate] [datetime2](7) NOT NULL,\r\n\t[ExpenseType] [nvarchar](100) NULL,\r\n\t[CreationDate] [datetime2](7) NOT NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[IsActive] [bit] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[ExpenseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[LicensePackages]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[LicensePackages]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[LicensePackages](\r\n\t[LicensePackageID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Name] [nvarchar](100) NOT NULL,\r\n\t[Description] [nvarchar](500) NULL,\r\n\t[Role] [nvarchar](50) NOT NULL,\r\n\t[DurationDays] [int] NOT NULL,\r\n\t[Price] [decimal](18, 2) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[LicensePackageID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[LicenseTransactions]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[LicenseTransactions]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[LicenseTransactions](\r\n\t[LicenseTransactionID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[UserID] [int] NOT NULL,\r\n\t[LicensePackageID] [int] NOT NULL,\r\n\t[UserLicenseID] [int] NULL,\r\n\t[Amount] [decimal](18, 2) NOT NULL,\r\n\t[PaymentMethod] [nvarchar](50) NOT NULL,\r\n\t[TransactionDate] [datetime] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[LicenseTransactionID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[MembershipFreezeHistory]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[MembershipFreezeHistory]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[MembershipFreezeHistory](\r\n\t[FreezeHistoryID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MembershipID] [int] NOT NULL,\r\n\t[StartDate] [datetime] NOT NULL,\r\n\t[PlannedEndDate] [datetime] NOT NULL,\r\n\t[ActualEndDate] [datetime] NULL,\r\n\t[FreezeDays] [int] NOT NULL,\r\n\t[UsedDays] [int] NULL,\r\n\t[CancellationType] [nvarchar](50) NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[FreezeHistoryID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Memberships]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Memberships]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Memberships](\r\n\t[MembershipID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MemberID] [int] NOT NULL,\r\n\t[MembershipTypeID] [int] NOT NULL,\r\n\t[StartDate] [datetime] NOT NULL,\r\n\t[EndDate] [datetime] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[IsFrozen] [bit] NOT NULL,\r\n\t[FreezeStartDate] [datetime] NULL,\r\n\t[FreezeEndDate] [datetime] NULL,\r\n\t[FreezeDays] [int] NULL,\r\n\t[OriginalEndDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n CONSTRAINT [PK_Memberships] PRIMARY KEY CLUSTERED \r\n(\r\n\t[MembershipID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[MembershipTypes]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipTypes' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[MembershipTypes]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[MembershipTypes](\r\n\t[MembershipTypeID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Branch] [varchar](50) NOT NULL,\r\n\t[TypeName] [nvarchar](50) NOT NULL,\r\n\t[Day] [int] NOT NULL,\r\n\t[Price] [decimal](10, 2) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n CONSTRAINT [PK_MembershipTypes] PRIMARY KEY CLUSTERED \r\n(\r\n\t[MembershipTypeID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipTypes' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[OperationClaims]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='OperationClaims' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[OperationClaims]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[OperationClaims](\r\n\t[OperationClaimID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Name] [varchar](50) NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_OperationClaims] PRIMARY KEY CLUSTERED \r\n(\r\n\t[OperationClaimID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='OperationClaims' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Payments]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Payments]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Payments](\r\n\t[PaymentID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MembershipID] [int] NOT NULL,\r\n\t[PaymentDate] [datetime] NOT NULL,\r\n\t[PaymentAmount] [decimal](10, 2) NOT NULL,\r\n\t[PaymentMethod] [varchar](50) NOT NULL,\r\n\t[PaymentStatus] [varchar](50) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[OriginalPaymentMethod] [nvarchar](50) NULL,\r\n\t[FinalPaymentMethod] [nvarchar](50) NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n CONSTRAINT [PK_Payments] PRIMARY KEY CLUSTERED \r\n(\r\n\t[PaymentID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products](\r\n\t[ProductID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Name] [nvarchar](100) NOT NULL,\r\n\t[Price] [decimal](10, 2) NOT NULL,\r\n\t[IsActive] [bit] NOT NULL,\r\n\t[CreationDate] [datetime2](7) NOT NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[ProductID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[RemainingDebts]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[RemainingDebts]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[RemainingDebts](\r\n\t[RemainingDebtID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[PaymentID] [int] NOT NULL,\r\n\t[OriginalAmount] [decimal](18, 2) NOT NULL,\r\n\t[RemainingAmount] [decimal](18, 2) NOT NULL,\r\n\t[LastUpdateDate] [datetime] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[RemainingDebtID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[SystemExercises]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[SystemExercises]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[SystemExercises](\r\n\t[SystemExerciseID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[ExerciseCategoryID] [int] NOT NULL,\r\n\t[ExerciseName] [nvarchar](200) NOT NULL,\r\n\t[Description] [nvarchar](1000) NULL,\r\n\t[Instructions] [nvarchar](2000) NULL,\r\n\t[MuscleGroups] [nvarchar](500) NULL,\r\n\t[Equipment] [nvarchar](200) NULL,\r\n\t[DifficultyLevel] [tinyint] NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime2](7) NULL,\r\n\t[DeletedDate] [datetime2](7) NULL,\r\n\t[UpdatedDate] [datetime2](7) NULL,\r\n CONSTRAINT [PK_SystemExercises] PRIMARY KEY CLUSTERED \r\n(\r\n\t[SystemExerciseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Towns]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Towns' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Towns]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Towns](\r\n\t[TownID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[CityID] [int] NOT NULL,\r\n\t[TownName] [varchar](50) NOT NULL,\r\n CONSTRAINT [PK_Towns] PRIMARY KEY CLUSTERED \r\n(\r\n\t[TownID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Towns' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Transactions]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Transactions]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Transactions](\r\n\t[TransactionID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[MemberID] [int] NOT NULL,\r\n\t[ProductID] [int] NULL,\r\n\t[Amount] [decimal](10, 2) NOT NULL,\r\n\t[TransactionType] [nvarchar](50) NOT NULL,\r\n\t[TransactionDate] [datetime2](7) NOT NULL,\r\n\t[Quantity] [int] NOT NULL,\r\n\t[IsPaid] [bit] NOT NULL,\r\n\t[UnitPrice] [decimal](18, 2) NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[TransactionID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[UserCompanies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[UserCompanies]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[UserCompanies](\r\n\t[UserCompanyID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[UserID] [int] NOT NULL,\r\n\t[CompanyID] [int] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_UserCompanies] PRIMARY KEY CLUSTERED \r\n(\r\n\t[UserCompanyID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[UserDevices]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[UserDevices]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[UserDevices](\r\n\t[Id] [int] IDENTITY(1,1) NOT NULL,\r\n\t[UserId] [int] NOT NULL,\r\n\t[RefreshToken] [varchar](500) NULL,\r\n\t[RefreshTokenExpiration] [datetime] NULL,\r\n\t[DeviceInfo] [nvarchar](500) NULL,\r\n\t[LastIpAddress] [varchar](50) NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreatedAt] [datetime] NULL,\r\n\t[LastUsedAt] [datetime] NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[Id] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[UserLicenses]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[UserLicenses]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[UserLicenses](\r\n\t[UserLicenseID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[UserID] [int] NOT NULL,\r\n\t[LicensePackageID] [int] NOT NULL,\r\n\t[StartDate] [datetime] NOT NULL,\r\n\t[EndDate] [datetime] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\nPRIMARY KEY CLUSTERED \r\n(\r\n\t[UserLicenseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[UserOperationClaims]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[UserOperationClaims]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[UserOperationClaims](\r\n\t[UserOperationClaimID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[UserID] [int] NOT NULL,\r\n\t[OperationClaimID] [int] NOT NULL,\r\n\t[IsActive] [bit] NULL,\r\n\t[CreationDate] [datetime] NULL,\r\n\t[DeletedDate] [datetime] NULL,\r\n\t[UpdatedDate] [datetime] NULL,\r\n CONSTRAINT [PK_UserOperationClaims] PRIMARY KEY CLUSTERED \r\n(\r\n\t[UserOperationClaimID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Users]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Users]    Script Date: 20.06.2025 07:53:52 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Users](\r\n\t[UserID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[FirstName] [varchar](50) NOT NULL,\r\n\t[LastName] [varchar](50) NULL,\r\n\t[Email] [varchar](100) NOT NULL,\r\n\t[IsActive] [bit] NOT NULL,\r\n\t[PasswordHash] [varbinary](500) NOT NULL,\r\n\t[PasswordSalt] [varbinary](500) NOT NULL,\r\n\t[CreationDate] [date] NULL,\r\n\t[DeletedDate] [date] NULL,\r\n\t[UpdatedDate] [date] NULL,\r\n\t[ProfileImagePath] [varchar](500) NULL,\r\n\t[RequirePasswordChange] [bit] NOT NULL,\r\n CONSTRAINT [PK_Users_1] PRIMARY KEY CLUSTERED \r\n(\r\n\t[UserID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [IX_CompanyExercises_CompanyID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_CompanyExercises_CompanyID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID] ON [dbo].[CompanyExercises]\r\n(\r\n\t[CompanyID] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_CompanyExercises_CompanyID_CategoryID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID_CategoryID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_CompanyExercises_CompanyID_CategoryID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID_CategoryID] ON [dbo].[CompanyExercises]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[ExerciseCategoryID] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID_CategoryID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_CompanyExercises_CompanyID_Name]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID_Name']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_CompanyExercises_CompanyID_Name]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyExercises_CompanyID_Name] ON [dbo].[CompanyExercises]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[ExerciseName] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Index[@Name='IX_CompanyExercises_CompanyID_Name']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_CompanyUsers_Email]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_Email']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_CompanyUsers_Email]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyUsers_Email] ON [dbo].[CompanyUsers]\r\n(\r\n\t[Email] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_Email']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_CompanyUsers_IsActive_CityID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_IsActive_CityID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_CompanyUsers_IsActive_CityID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyUsers_IsActive_CityID] ON [dbo].[CompanyUsers]\r\n(\r\n\t[IsActive] ASC,\r\n\t[CityID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_IsActive_CityID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_CompanyUsers_PhoneNumber]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_PhoneNumber']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_CompanyUsers_PhoneNumber]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_CompanyUsers_PhoneNumber] ON [dbo].[CompanyUsers]\r\n(\r\n\t[PhoneNumber] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyUsers' and @Schema='dbo']/Index[@Name='IX_CompanyUsers_PhoneNumber']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_EntryExitHistories_EntryDate_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Index[@Name='IX_EntryExitHistories_EntryDate_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_EntryExitHistories_EntryDate_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_EntryExitHistories_EntryDate_IsActive] ON [dbo].[EntryExitHistories]\r\n(\r\n\t[EntryDate] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Index[@Name='IX_EntryExitHistories_EntryDate_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_EntryExitHistories_MembershipID_EntryDate]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Index[@Name='IX_EntryExitHistories_MembershipID_EntryDate']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_EntryExitHistories_MembershipID_EntryDate]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_EntryExitHistories_MembershipID_EntryDate] ON [dbo].[EntryExitHistories]\r\n(\r\n\t[MembershipID] ASC,\r\n\t[EntryDate] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Index[@Name='IX_EntryExitHistories_MembershipID_EntryDate']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_ExerciseCategories_CategoryName]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Index[@Name='IX_ExerciseCategories_CategoryName']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_ExerciseCategories_CategoryName]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_ExerciseCategories_CategoryName] ON [dbo].[ExerciseCategories]\r\n(\r\n\t[CategoryName] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Index[@Name='IX_ExerciseCategories_CategoryName']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_ExerciseCategories_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Index[@Name='IX_ExerciseCategories_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_ExerciseCategories_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_ExerciseCategories_IsActive] ON [dbo].[ExerciseCategories]\r\n(\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Index[@Name='IX_ExerciseCategories_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Expenses_CompanyID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Index[@Name='IX_Expenses_CompanyID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Expenses_CompanyID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Expenses_CompanyID] ON [dbo].[Expenses]\r\n(\r\n\t[CompanyID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Index[@Name='IX_Expenses_CompanyID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Expenses_ExpenseDate]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Index[@Name='IX_Expenses_ExpenseDate']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Expenses_ExpenseDate]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Expenses_ExpenseDate] ON [dbo].[Expenses]\r\n(\r\n\t[ExpenseDate] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Index[@Name='IX_Expenses_ExpenseDate']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Members_IsActive_Name]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_IsActive_Name']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Members_IsActive_Name]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Members_IsActive_Name] ON [dbo].[Members]\r\n(\r\n\t[IsActive] ASC,\r\n\t[Name] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_IsActive_Name']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Members_PhoneNumber]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_PhoneNumber']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Members_PhoneNumber]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Members_PhoneNumber] ON [dbo].[Members]\r\n(\r\n\t[PhoneNumber] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_PhoneNumber']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Members_ScanNumber]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_ScanNumber']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Members_ScanNumber]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Members_ScanNumber] ON [dbo].[Members]\r\n(\r\n\t[ScanNumber] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Index[@Name='IX_Members_ScanNumber']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Memberships_EndDate_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_EndDate_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Memberships_EndDate_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Memberships_EndDate_IsActive] ON [dbo].[Memberships]\r\n(\r\n\t[EndDate] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_EndDate_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Memberships_MemberID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_MemberID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Memberships_MemberID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Memberships_MemberID_IsActive] ON [dbo].[Memberships]\r\n(\r\n\t[MemberID] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_MemberID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Memberships_StartDate_EndDate_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_StartDate_EndDate_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Memberships_StartDate_EndDate_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Memberships_StartDate_EndDate_IsActive] ON [dbo].[Memberships]\r\n(\r\n\t[StartDate] ASC,\r\n\t[EndDate] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Index[@Name='IX_Memberships_StartDate_EndDate_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_MemberWorkoutPrograms_CompanyID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_CompanyID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_MemberWorkoutPrograms_CompanyID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CompanyID_IsActive] ON [dbo].[MemberWorkoutPrograms]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[IsActive] ASC\r\n)\r\nINCLUDE([MemberID],[WorkoutProgramTemplateID],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_CompanyID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_MemberWorkoutPrograms_CreationDate]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_CreationDate']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_MemberWorkoutPrograms_CreationDate]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CreationDate] ON [dbo].[MemberWorkoutPrograms]\r\n(\r\n\t[CreationDate] DESC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_CreationDate']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_MemberWorkoutPrograms_Member_Company_Active]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_Member_Company_Active']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_MemberWorkoutPrograms_Member_Company_Active]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_Member_Company_Active] ON [dbo].[MemberWorkoutPrograms]\r\n(\r\n\t[MemberID] ASC,\r\n\t[CompanyID] ASC,\r\n\t[IsActive] ASC\r\n)\r\nINCLUDE([WorkoutProgramTemplateID],[StartDate],[EndDate],[Notes]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_Member_Company_Active']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_MemberWorkoutPrograms_MemberID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_MemberID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_MemberWorkoutPrograms_MemberID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_MemberID_IsActive] ON [dbo].[MemberWorkoutPrograms]\r\n(\r\n\t[MemberID] ASC,\r\n\t[IsActive] ASC\r\n)\r\nINCLUDE([WorkoutProgramTemplateID],[StartDate],[EndDate],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_MemberID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_MemberWorkoutPrograms_TemplateID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_TemplateID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_MemberWorkoutPrograms_TemplateID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_TemplateID_IsActive] ON [dbo].[MemberWorkoutPrograms]\r\n(\r\n\t[WorkoutProgramTemplateID] ASC,\r\n\t[IsActive] ASC\r\n)\r\nINCLUDE([MemberID],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Index[@Name='IX_MemberWorkoutPrograms_TemplateID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Payments_MembershipID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_MembershipID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Payments_MembershipID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Payments_MembershipID_IsActive] ON [dbo].[Payments]\r\n(\r\n\t[MembershipID] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_MembershipID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Payments_PaymentDate_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_PaymentDate_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Payments_PaymentDate_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Payments_PaymentDate_IsActive] ON [dbo].[Payments]\r\n(\r\n\t[PaymentDate] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_PaymentDate_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Payments_PaymentStatus]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_PaymentStatus']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Payments_PaymentStatus]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Payments_PaymentStatus] ON [dbo].[Payments]\r\n(\r\n\t[PaymentStatus] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Index[@Name='IX_Payments_PaymentStatus']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_RemainingDebts_LastUpdateDate_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Index[@Name='IX_RemainingDebts_LastUpdateDate_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_RemainingDebts_LastUpdateDate_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_RemainingDebts_LastUpdateDate_IsActive] ON [dbo].[RemainingDebts]\r\n(\r\n\t[LastUpdateDate] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Index[@Name='IX_RemainingDebts_LastUpdateDate_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_RemainingDebts_PaymentID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Index[@Name='IX_RemainingDebts_PaymentID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_RemainingDebts_PaymentID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_RemainingDebts_PaymentID_IsActive] ON [dbo].[RemainingDebts]\r\n(\r\n\t[PaymentID] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Index[@Name='IX_RemainingDebts_PaymentID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_SystemExercises_CategoryID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_CategoryID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_SystemExercises_CategoryID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_SystemExercises_CategoryID] ON [dbo].[SystemExercises]\r\n(\r\n\t[ExerciseCategoryID] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_CategoryID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_SystemExercises_DifficultyLevel]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_DifficultyLevel']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_SystemExercises_DifficultyLevel]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_SystemExercises_DifficultyLevel] ON [dbo].[SystemExercises]\r\n(\r\n\t[DifficultyLevel] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_DifficultyLevel']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_SystemExercises_Name]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_Name']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_SystemExercises_Name]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_SystemExercises_Name] ON [dbo].[SystemExercises]\r\n(\r\n\t[ExerciseName] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Index[@Name='IX_SystemExercises_Name']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Transactions_MemberID_TransactionDate]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Index[@Name='IX_Transactions_MemberID_TransactionDate']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Transactions_MemberID_TransactionDate]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Transactions_MemberID_TransactionDate] ON [dbo].[Transactions]\r\n(\r\n\t[MemberID] ASC,\r\n\t[TransactionDate] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Index[@Name='IX_Transactions_MemberID_TransactionDate']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Transactions_TransactionDate_IsPaid]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Index[@Name='IX_Transactions_TransactionDate_IsPaid']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_Transactions_TransactionDate_IsPaid]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Transactions_TransactionDate_IsPaid] ON [dbo].[Transactions]\r\n(\r\n\t[TransactionDate] ASC,\r\n\t[IsPaid] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Index[@Name='IX_Transactions_TransactionDate_IsPaid']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_UserOperationClaims_OperationClaimID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/Index[@Name='IX_UserOperationClaims_OperationClaimID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_UserOperationClaims_OperationClaimID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_UserOperationClaims_OperationClaimID_IsActive] ON [dbo].[UserOperationClaims]\r\n(\r\n\t[OperationClaimID] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/Index[@Name='IX_UserOperationClaims_OperationClaimID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_UserOperationClaims_UserID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/Index[@Name='IX_UserOperationClaims_UserID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_UserOperationClaims_UserID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_UserOperationClaims_UserID_IsActive] ON [dbo].[UserOperationClaims]\r\n(\r\n\t[UserID] ASC,\r\n\t[IsActive] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/Index[@Name='IX_UserOperationClaims_UserID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Users_Email]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_Email']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Users_Email]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Users_Email] ON [dbo].[Users]\r\n(\r\n\t[Email] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_Email']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Users_IsActive_Email]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_IsActive_Email']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Users_IsActive_Email]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Users_IsActive_Email] ON [dbo].[Users]\r\n(\r\n\t[IsActive] ASC,\r\n\t[Email] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_IsActive_Email']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_Users_ProfileImagePath]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_ProfileImagePath']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_Users_ProfileImagePath]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_Users_ProfileImagePath] ON [dbo].[Users]\r\n(\r\n\t[ProfileImagePath] ASC\r\n)\r\nWHERE ([ProfileImagePath] IS NOT NULL)\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Index[@Name='IX_Users_ProfileImagePath']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramDays_CompanyID_TemplateID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_CompanyID_TemplateID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramDays_CompanyID_TemplateID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_CompanyID_TemplateID] ON [dbo].[WorkoutProgramDays]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[WorkoutProgramTemplateID] ASC\r\n)\r\nINCLUDE([DayNumber],[DayName],[IsRestDay]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_CompanyID_TemplateID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramDays_TemplateID_DayNumber]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_TemplateID_DayNumber']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramDays_TemplateID_DayNumber]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramDays_TemplateID_DayNumber] ON [dbo].[WorkoutProgramDays]\r\n(\r\n\t[WorkoutProgramTemplateID] ASC,\r\n\t[DayNumber] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_TemplateID_DayNumber']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramDays_TemplateID_DayNumber_Unique]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_TemplateID_DayNumber_Unique']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramDays_TemplateID_DayNumber_Unique]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramDays_TemplateID_DayNumber_Unique] ON [dbo].[WorkoutProgramDays]\r\n(\r\n\t[WorkoutProgramTemplateID] ASC,\r\n\t[DayNumber] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramDays_TemplateID_DayNumber_Unique']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex] ON [dbo].[WorkoutProgramExercises]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[WorkoutProgramDayID] ASC,\r\n\t[OrderIndex] ASC\r\n)\r\nINCLUDE([ExerciseType],[ExerciseID],[Sets],[Reps]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_CompanyID_DayID_OrderIndex']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramExercises_DayID_OrderIndex_Unique]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_DayID_OrderIndex_Unique']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramExercises_DayID_OrderIndex_Unique]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_DayID_OrderIndex_Unique] ON [dbo].[WorkoutProgramExercises]\r\n(\r\n\t[WorkoutProgramDayID] ASC,\r\n\t[OrderIndex] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_DayID_OrderIndex_Unique']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramExercises_ExerciseType_ExerciseID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_ExerciseType_ExerciseID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_WorkoutProgramExercises_ExerciseType_ExerciseID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramExercises_ExerciseType_ExerciseID] ON [dbo].[WorkoutProgramExercises]\r\n(\r\n\t[ExerciseType] ASC,\r\n\t[ExerciseID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramExercises_ExerciseType_ExerciseID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramTemplates_CompanyID_IsActive]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_CompanyID_IsActive']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Index [IX_WorkoutProgramTemplates_CompanyID_IsActive]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_CompanyID_IsActive] ON [dbo].[WorkoutProgramTemplates]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[IsActive] ASC\r\n)\r\nINCLUDE([ProgramName],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_CompanyID_IsActive']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE UNIQUE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique] ON [dbo].[WorkoutProgramTemplates]\r\n(\r\n\t[CompanyID] ASC,\r\n\t[ProgramName] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, IGNORE_DUP_KEY = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_CompanyID_ProgramName_Unique']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_WorkoutProgramTemplates_ProgramName]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_ProgramName']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ANSI_PADDING ON\r\nGO\r\n","/****** Object:  Index [IX_WorkoutProgramTemplates_ProgramName]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_WorkoutProgramTemplates_ProgramName] ON [dbo].[WorkoutProgramTemplates]\r\n(\r\n\t[ProgramName] ASC\r\n)\r\nWHERE ([IsActive]=(1))\r\nWITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Index[@Name='IX_WorkoutProgramTemplates_ProgramName']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_vw_MemberWorkoutProgramDetails_CompanyID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_CompanyID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ARITHABORT ON\r\nSET CONCAT_NULL_YIELDS_NULL ON\r\nSET QUOTED_IDENTIFIER ON\r\nSET ANSI_NULLS ON\r\nSET ANSI_PADDING ON\r\nSET ANSI_WARNINGS ON\r\nSET NUMERIC_ROUNDABORT OFF\r\nGO\r\n","/****** Object:  Index [IX_vw_MemberWorkoutProgramDetails_CompanyID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_CompanyID] ON [dbo].[vw_MemberWorkoutProgramDetails]\r\n(\r\n\t[CompanyID] ASC\r\n)\r\nINCLUDE([MemberName],[ProgramName],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_CompanyID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_vw_MemberWorkoutProgramDetails_MemberID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_MemberID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ARITHABORT ON\r\nSET CONCAT_NULL_YIELDS_NULL ON\r\nSET QUOTED_IDENTIFIER ON\r\nSET ANSI_NULLS ON\r\nSET ANSI_PADDING ON\r\nSET ANSI_WARNINGS ON\r\nSET NUMERIC_ROUNDABORT OFF\r\nGO\r\n","/****** Object:  Index [IX_vw_MemberWorkoutProgramDetails_MemberID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_MemberID] ON [dbo].[vw_MemberWorkoutProgramDetails]\r\n(\r\n\t[MemberID] ASC\r\n)\r\nINCLUDE([ProgramName],[StartDate],[EndDate],[ExperienceLevel]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_MemberID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [IX_vw_MemberWorkoutProgramDetails_TemplateID]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_TemplateID']","object_type":"Index"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["SET ARITHABORT ON\r\nSET CONCAT_NULL_YIELDS_NULL ON\r\nSET QUOTED_IDENTIFIER ON\r\nSET ANSI_NULLS ON\r\nSET ANSI_PADDING ON\r\nSET ANSI_WARNINGS ON\r\nSET NUMERIC_ROUNDABORT OFF\r\nGO\r\n","/****** Object:  Index [IX_vw_MemberWorkoutProgramDetails_TemplateID]    Script Date: 20.06.2025 07:53:52 ******/\r\nCREATE NONCLUSTERED INDEX [IX_vw_MemberWorkoutProgramDetails_TemplateID] ON [dbo].[vw_MemberWorkoutProgramDetails]\r\n(\r\n\t[WorkoutProgramTemplateID] ASC\r\n)\r\nINCLUDE([MemberName],[CreationDate]) WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, SORT_IN_TEMPDB = OFF, DROP_EXISTING = OFF, ONLINE = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/View[@Name='vw_MemberWorkoutProgramDetails' and @Schema='dbo']/Index[@Name='IX_vw_MemberWorkoutProgramDetails_TemplateID']","object_type":"Index"}},{"cell_type":"markdown","source":["# [DF__CompanyEx__IsAct__4B2D1C3C]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__CompanyEx__IsAct__4B2D1C3C']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyExercises] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__CompanyEx__IsAct__4B2D1C3C']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__CompanyEx__Creat__4C214075]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__CompanyEx__Creat__4C214075']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyExercises] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__CompanyEx__Creat__4C214075']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__DebtPayme__Payme__5C8CB268]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='PaymentDate']/Default[@Name='DF__DebtPayme__Payme__5C8CB268']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[DebtPayments] ADD  DEFAULT (getdate()) FOR [PaymentDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='PaymentDate']/Default[@Name='DF__DebtPayme__Payme__5C8CB268']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__DebtPayme__IsAct__5D80D6A1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__DebtPayme__IsAct__5D80D6A1']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[DebtPayments] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__DebtPayme__IsAct__5D80D6A1']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__DebtPayme__Creat__5E74FADA]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__DebtPayme__Creat__5E74FADA']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[DebtPayments] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__DebtPayme__Creat__5E74FADA']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__DebtPayme__Compa__0B47A151]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__DebtPayme__Compa__0B47A151']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[DebtPayments] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__DebtPayme__Compa__0B47A151']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__EntryExit__Compa__086B34A6]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__EntryExit__Compa__086B34A6']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[EntryExitHistories] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__EntryExit__Compa__086B34A6']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__ExerciseC__IsAct__4297D63B]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__ExerciseC__IsAct__4297D63B']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[ExerciseCategories] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__ExerciseC__IsAct__4297D63B']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__ExerciseC__Creat__438BFA74]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__ExerciseC__Creat__438BFA74']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[ExerciseCategories] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='ExerciseCategories' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__ExerciseC__Creat__438BFA74']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Expenses__Creati__0F183235]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Expenses__Creati__0F183235']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Expenses] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Expenses__Creati__0F183235']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Expenses__IsActi__100C566E]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Expenses__IsActi__100C566E']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Expenses] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Expenses__IsActi__100C566E']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__LicensePa__IsAct__75586032]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__LicensePa__IsAct__75586032']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicensePackages] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__LicensePa__IsAct__75586032']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__LicensePa__Creat__764C846B]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__LicensePa__Creat__764C846B']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicensePackages] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicensePackages' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__LicensePa__Creat__764C846B']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__LicenseTr__IsAct__7EE1CA6C]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__LicenseTr__IsAct__7EE1CA6C']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicenseTransactions] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__LicenseTr__IsAct__7EE1CA6C']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__LicenseTr__Creat__7FD5EEA5]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__LicenseTr__Creat__7FD5EEA5']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicenseTransactions] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__LicenseTr__Creat__7FD5EEA5']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Members__Balance__216BEC9A]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Column[@Name='Balance']/Default[@Name='DF__Members__Balance__216BEC9A']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Members] ADD  DEFAULT ((0.00)) FOR [Balance]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Column[@Name='Balance']/Default[@Name='DF__Members__Balance__216BEC9A']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Members__Company__03A67F89]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Members__Company__03A67F89']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Members] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Members' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Members__Company__03A67F89']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__Creat__7187CF4E]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Membershi__Creat__7187CF4E']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MembershipFreezeHistory] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Membershi__Creat__7187CF4E']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__Compa__095F58DF]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__095F58DF']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MembershipFreezeHistory] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__095F58DF']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__IsFro__6DB73E6A]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='IsFrozen']/Default[@Name='DF__Membershi__IsFro__6DB73E6A']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Memberships] ADD  DEFAULT ((0)) FOR [IsFrozen]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='IsFrozen']/Default[@Name='DF__Membershi__IsFro__6DB73E6A']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__Freez__6EAB62A3]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='FreezeDays']/Default[@Name='DF__Membershi__Freez__6EAB62A3']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Memberships] ADD  DEFAULT ((0)) FOR [FreezeDays]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='FreezeDays']/Default[@Name='DF__Membershi__Freez__6EAB62A3']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__Compa__0C3BC58A]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__0C3BC58A']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Memberships] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__0C3BC58A']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Membershi__Compa__049AA3C2]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipTypes' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__049AA3C2']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MembershipTypes] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipTypes' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Membershi__Compa__049AA3C2']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__MemberWor__IsAct__3726238F]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__MemberWor__IsAct__3726238F']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__MemberWor__IsAct__3726238F']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__MemberWor__Creat__381A47C8]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__MemberWor__Creat__381A47C8']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__MemberWor__Creat__381A47C8']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Payments__Compan__058EC7FB]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Payments__Compan__058EC7FB']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Payments] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Payments__Compan__058EC7FB']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Products__IsActi__24485945]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Products__IsActi__24485945']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Products] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Products__IsActi__24485945']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Products__Creati__253C7D7E]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Products__Creati__253C7D7E']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Products] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Products__Creati__253C7D7E']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Products__Compan__0682EC34]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Products__Compan__0682EC34']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Products] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Products' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Products__Compan__0682EC34']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Remaining__IsAct__57C7FD4B]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Remaining__IsAct__57C7FD4B']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[RemainingDebts] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__Remaining__IsAct__57C7FD4B']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Remaining__Creat__58BC2184]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Remaining__Creat__58BC2184']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[RemainingDebts] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__Remaining__Creat__58BC2184']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Remaining__Compa__0A537D18]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Remaining__Compa__0A537D18']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[RemainingDebts] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Remaining__Compa__0A537D18']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__SystemExe__IsAct__4668671F]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__SystemExe__IsAct__4668671F']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[SystemExercises] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__SystemExe__IsAct__4668671F']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__SystemExe__Creat__475C8B58]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__SystemExe__Creat__475C8B58']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[SystemExercises] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__SystemExe__Creat__475C8B58']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Transacti__Trans__2818EA29]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='TransactionDate']/Default[@Name='DF__Transacti__Trans__2818EA29']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions] ADD  DEFAULT (getdate()) FOR [TransactionDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='TransactionDate']/Default[@Name='DF__Transacti__Trans__2818EA29']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Transacti__Quant__2FBA0BF1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='Quantity']/Default[@Name='DF__Transacti__Quant__2FBA0BF1']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions] ADD  DEFAULT ((0)) FOR [Quantity]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='Quantity']/Default[@Name='DF__Transacti__Quant__2FBA0BF1']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Transacti__IsPai__42CCE065]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='IsPaid']/Default[@Name='DF__Transacti__IsPai__42CCE065']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions] ADD  DEFAULT ((0)) FOR [IsPaid]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='IsPaid']/Default[@Name='DF__Transacti__IsPai__42CCE065']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Transacti__UnitP__43C1049E]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='UnitPrice']/Default[@Name='DF__Transacti__UnitP__43C1049E']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions] ADD  DEFAULT ((0)) FOR [UnitPrice]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='UnitPrice']/Default[@Name='DF__Transacti__UnitP__43C1049E']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Transacti__Compa__0777106D]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Transacti__Compa__0777106D']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions] ADD  DEFAULT ((1)) FOR [CompanyID]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/Column[@Name='CompanyID']/Default[@Name='DF__Transacti__Compa__0777106D']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__UserDevic__IsAct__6ADAD1BF]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__UserDevic__IsAct__6ADAD1BF']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserDevices] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__UserDevic__IsAct__6ADAD1BF']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__UserDevic__Creat__6BCEF5F8]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/Column[@Name='CreatedAt']/Default[@Name='DF__UserDevic__Creat__6BCEF5F8']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserDevices] ADD  DEFAULT (getdate()) FOR [CreatedAt]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/Column[@Name='CreatedAt']/Default[@Name='DF__UserDevic__Creat__6BCEF5F8']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__UserLicen__IsAct__7928F116]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__UserLicen__IsAct__7928F116']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserLicenses] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__UserLicen__IsAct__7928F116']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__UserLicen__Creat__7A1D154F]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__UserLicen__Creat__7A1D154F']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserLicenses] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__UserLicen__Creat__7A1D154F']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__Users__RequirePa__4EFDAD20]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Column[@Name='RequirePasswordChange']/Default[@Name='DF__Users__RequirePa__4EFDAD20']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Users] ADD  DEFAULT ((0)) FOR [RequirePasswordChange]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Users' and @Schema='dbo']/Column[@Name='RequirePasswordChange']/Default[@Name='DF__Users__RequirePa__4EFDAD20']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__WorkoutPr__IsRes__28D80438]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Column[@Name='IsRestDay']/Default[@Name='DF__WorkoutPr__IsRes__28D80438']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramDays] ADD  DEFAULT ((0)) FOR [IsRestDay]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Column[@Name='IsRestDay']/Default[@Name='DF__WorkoutPr__IsRes__28D80438']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__WorkoutPr__Creat__29CC2871]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__29CC2871']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramDays] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__29CC2871']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__WorkoutPr__Creat__2E90DD8E]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__2E90DD8E']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__2E90DD8E']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__WorkoutPr__IsAct__24134F1B]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__WorkoutPr__IsAct__24134F1B']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramTemplates] ADD  DEFAULT ((1)) FOR [IsActive]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Column[@Name='IsActive']/Default[@Name='DF__WorkoutPr__IsAct__24134F1B']","object_type":"Default"}},{"cell_type":"markdown","source":["# [DF__WorkoutPr__Creat__25077354]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__25077354']","object_type":"Default"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramTemplates] ADD  DEFAULT (getdate()) FOR [CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/Column[@Name='CreationDate']/Default[@Name='DF__WorkoutPr__Creat__25077354']","object_type":"Default"}},{"cell_type":"markdown","source":["# [FK_CompanyAdresses_Cities]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Cities']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyAdresses]  WITH CHECK ADD  CONSTRAINT [FK_CompanyAdresses_Cities] FOREIGN KEY([CityID])\r\nREFERENCES [dbo].[Cities] ([CityID])\r\n","GO\r\n","ALTER TABLE [dbo].[CompanyAdresses] CHECK CONSTRAINT [FK_CompanyAdresses_Cities]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Cities']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_CompanyAdresses_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyAdresses]  WITH CHECK ADD  CONSTRAINT [FK_CompanyAdresses_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[CompanyAdresses] CHECK CONSTRAINT [FK_CompanyAdresses_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_CompanyAdresses_Towns]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Towns']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyAdresses]  WITH CHECK ADD  CONSTRAINT [FK_CompanyAdresses_Towns] FOREIGN KEY([TownID])\r\nREFERENCES [dbo].[Towns] ([TownID])\r\n","GO\r\n","ALTER TABLE [dbo].[CompanyAdresses] CHECK CONSTRAINT [FK_CompanyAdresses_Towns]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyAdresses' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyAdresses_Towns']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_CompanyExercises_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyExercises_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyExercises]  WITH CHECK ADD  CONSTRAINT [FK_CompanyExercises_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[CompanyExercises] CHECK CONSTRAINT [FK_CompanyExercises_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyExercises_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_CompanyExercises_ExerciseCategories]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyExercises_ExerciseCategories']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[CompanyExercises]  WITH CHECK ADD  CONSTRAINT [FK_CompanyExercises_ExerciseCategories] FOREIGN KEY([ExerciseCategoryID])\r\nREFERENCES [dbo].[ExerciseCategories] ([ExerciseCategoryID])\r\n","GO\r\n","ALTER TABLE [dbo].[CompanyExercises] CHECK CONSTRAINT [FK_CompanyExercises_ExerciseCategories]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='CompanyExercises' and @Schema='dbo']/ForeignKey[@Name='FK_CompanyExercises_ExerciseCategories']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__DebtPayme__Remai__5F691F13]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/ForeignKey[@Name='FK__DebtPayme__Remai__5F691F13']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[DebtPayments]  WITH CHECK ADD FOREIGN KEY([RemainingDebtID])\r\nREFERENCES [dbo].[RemainingDebts] ([RemainingDebtID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='DebtPayments' and @Schema='dbo']/ForeignKey[@Name='FK__DebtPayme__Remai__5F691F13']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_EntryExitHistories_Memberships1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/ForeignKey[@Name='FK_EntryExitHistories_Memberships1']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[EntryExitHistories]  WITH CHECK ADD  CONSTRAINT [FK_EntryExitHistories_Memberships1] FOREIGN KEY([MembershipID])\r\nREFERENCES [dbo].[Memberships] ([MembershipID])\r\n","GO\r\n","ALTER TABLE [dbo].[EntryExitHistories] CHECK CONSTRAINT [FK_EntryExitHistories_Memberships1]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='EntryExitHistories' and @Schema='dbo']/ForeignKey[@Name='FK_EntryExitHistories_Memberships1']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Expenses_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/ForeignKey[@Name='FK_Expenses_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Expenses]  WITH CHECK ADD  CONSTRAINT [FK_Expenses_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[Expenses] CHECK CONSTRAINT [FK_Expenses_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Expenses' and @Schema='dbo']/ForeignKey[@Name='FK_Expenses_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__LicenseTr__Licen__01BE3717]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__Licen__01BE3717']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicenseTransactions]  WITH CHECK ADD FOREIGN KEY([LicensePackageID])\r\nREFERENCES [dbo].[LicensePackages] ([LicensePackageID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__Licen__01BE3717']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__LicenseTr__UserI__00CA12DE]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__UserI__00CA12DE']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicenseTransactions]  WITH CHECK ADD FOREIGN KEY([UserID])\r\nREFERENCES [dbo].[Users] ([UserID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__UserI__00CA12DE']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__LicenseTr__UserL__02B25B50]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__UserL__02B25B50']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[LicenseTransactions]  WITH CHECK ADD FOREIGN KEY([UserLicenseID])\r\nREFERENCES [dbo].[UserLicenses] ([UserLicenseID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='LicenseTransactions' and @Schema='dbo']/ForeignKey[@Name='FK__LicenseTr__UserL__02B25B50']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__Membershi__Membe__727BF387]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/ForeignKey[@Name='FK__Membershi__Membe__727BF387']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MembershipFreezeHistory]  WITH CHECK ADD FOREIGN KEY([MembershipID])\r\nREFERENCES [dbo].[Memberships] ([MembershipID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MembershipFreezeHistory' and @Schema='dbo']/ForeignKey[@Name='FK__Membershi__Membe__727BF387']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Memberships_Members1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/ForeignKey[@Name='FK_Memberships_Members1']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Memberships]  WITH CHECK ADD  CONSTRAINT [FK_Memberships_Members1] FOREIGN KEY([MemberID])\r\nREFERENCES [dbo].[Members] ([MemberID])\r\n","GO\r\n","ALTER TABLE [dbo].[Memberships] CHECK CONSTRAINT [FK_Memberships_Members1]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/ForeignKey[@Name='FK_Memberships_Members1']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Memberships_MembershipTypes]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/ForeignKey[@Name='FK_Memberships_MembershipTypes']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Memberships]  WITH CHECK ADD  CONSTRAINT [FK_Memberships_MembershipTypes] FOREIGN KEY([MembershipTypeID])\r\nREFERENCES [dbo].[MembershipTypes] ([MembershipTypeID])\r\n","GO\r\n","ALTER TABLE [dbo].[Memberships] CHECK CONSTRAINT [FK_Memberships_MembershipTypes]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Memberships' and @Schema='dbo']/ForeignKey[@Name='FK_Memberships_MembershipTypes']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_MemberWorkoutPrograms_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms]  WITH CHECK ADD  CONSTRAINT [FK_MemberWorkoutPrograms_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[MemberWorkoutPrograms] CHECK CONSTRAINT [FK_MemberWorkoutPrograms_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_MemberWorkoutPrograms_Members]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_Members']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms]  WITH CHECK ADD  CONSTRAINT [FK_MemberWorkoutPrograms_Members] FOREIGN KEY([MemberID])\r\nREFERENCES [dbo].[Members] ([MemberID])\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[MemberWorkoutPrograms] CHECK CONSTRAINT [FK_MemberWorkoutPrograms_Members]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_Members']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_MemberWorkoutPrograms_WorkoutProgramTemplates]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_WorkoutProgramTemplates']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms]  WITH CHECK ADD  CONSTRAINT [FK_MemberWorkoutPrograms_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID])\r\nREFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID])\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[MemberWorkoutPrograms] CHECK CONSTRAINT [FK_MemberWorkoutPrograms_WorkoutProgramTemplates]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/ForeignKey[@Name='FK_MemberWorkoutPrograms_WorkoutProgramTemplates']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Payments_Memberships]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/ForeignKey[@Name='FK_Payments_Memberships']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Payments]  WITH CHECK ADD  CONSTRAINT [FK_Payments_Memberships] FOREIGN KEY([MembershipID])\r\nREFERENCES [dbo].[Memberships] ([MembershipID])\r\n","GO\r\n","ALTER TABLE [dbo].[Payments] CHECK CONSTRAINT [FK_Payments_Memberships]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Payments' and @Schema='dbo']/ForeignKey[@Name='FK_Payments_Memberships']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__Remaining__Payme__59B045BD]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/ForeignKey[@Name='FK__Remaining__Payme__59B045BD']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[RemainingDebts]  WITH CHECK ADD FOREIGN KEY([PaymentID])\r\nREFERENCES [dbo].[Payments] ([PaymentID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='RemainingDebts' and @Schema='dbo']/ForeignKey[@Name='FK__Remaining__Payme__59B045BD']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_SystemExercises_ExerciseCategories]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/ForeignKey[@Name='FK_SystemExercises_ExerciseCategories']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[SystemExercises]  WITH CHECK ADD  CONSTRAINT [FK_SystemExercises_ExerciseCategories] FOREIGN KEY([ExerciseCategoryID])\r\nREFERENCES [dbo].[ExerciseCategories] ([ExerciseCategoryID])\r\n","GO\r\n","ALTER TABLE [dbo].[SystemExercises] CHECK CONSTRAINT [FK_SystemExercises_ExerciseCategories]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='SystemExercises' and @Schema='dbo']/ForeignKey[@Name='FK_SystemExercises_ExerciseCategories']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Towns_Cities]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Towns' and @Schema='dbo']/ForeignKey[@Name='FK_Towns_Cities']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Towns]  WITH CHECK ADD  CONSTRAINT [FK_Towns_Cities] FOREIGN KEY([CityID])\r\nREFERENCES [dbo].[Cities] ([CityID])\r\n","GO\r\n","ALTER TABLE [dbo].[Towns] CHECK CONSTRAINT [FK_Towns_Cities]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Towns' and @Schema='dbo']/ForeignKey[@Name='FK_Towns_Cities']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__Transacti__Membe__290D0E62]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/ForeignKey[@Name='FK__Transacti__Membe__290D0E62']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions]  WITH CHECK ADD FOREIGN KEY([MemberID])\r\nREFERENCES [dbo].[Members] ([MemberID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/ForeignKey[@Name='FK__Transacti__Membe__290D0E62']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__Transacti__Produ__2A01329B]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/ForeignKey[@Name='FK__Transacti__Produ__2A01329B']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Transactions]  WITH CHECK ADD FOREIGN KEY([ProductID])\r\nREFERENCES [dbo].[Products] ([ProductID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='Transactions' and @Schema='dbo']/ForeignKey[@Name='FK__Transacti__Produ__2A01329B']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_UserCompanies_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']/ForeignKey[@Name='FK_UserCompanies_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserCompanies]  WITH CHECK ADD  CONSTRAINT [FK_UserCompanies_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[UserCompanies] CHECK CONSTRAINT [FK_UserCompanies_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']/ForeignKey[@Name='FK_UserCompanies_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_UserCompanies_CompanyUsers]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']/ForeignKey[@Name='FK_UserCompanies_CompanyUsers']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserCompanies]  WITH CHECK ADD  CONSTRAINT [FK_UserCompanies_CompanyUsers] FOREIGN KEY([UserID])\r\nREFERENCES [dbo].[CompanyUsers] ([CompanyUserID])\r\n","GO\r\n","ALTER TABLE [dbo].[UserCompanies] CHECK CONSTRAINT [FK_UserCompanies_CompanyUsers]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserCompanies' and @Schema='dbo']/ForeignKey[@Name='FK_UserCompanies_CompanyUsers']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__UserDevic__UserI__6CC31A31]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/ForeignKey[@Name='FK__UserDevic__UserI__6CC31A31']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserDevices]  WITH CHECK ADD FOREIGN KEY([UserId])\r\nREFERENCES [dbo].[Users] ([UserID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserDevices' and @Schema='dbo']/ForeignKey[@Name='FK__UserDevic__UserI__6CC31A31']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__UserLicen__Licen__7C055DC1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/ForeignKey[@Name='FK__UserLicen__Licen__7C055DC1']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserLicenses]  WITH CHECK ADD FOREIGN KEY([LicensePackageID])\r\nREFERENCES [dbo].[LicensePackages] ([LicensePackageID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/ForeignKey[@Name='FK__UserLicen__Licen__7C055DC1']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK__UserLicen__UserI__7B113988]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/ForeignKey[@Name='FK__UserLicen__UserI__7B113988']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserLicenses]  WITH CHECK ADD FOREIGN KEY([UserID])\r\nREFERENCES [dbo].[Users] ([UserID])\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserLicenses' and @Schema='dbo']/ForeignKey[@Name='FK__UserLicen__UserI__7B113988']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_UserOperationClaims_OperationClaims]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/ForeignKey[@Name='FK_UserOperationClaims_OperationClaims']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserOperationClaims]  WITH CHECK ADD  CONSTRAINT [FK_UserOperationClaims_OperationClaims] FOREIGN KEY([OperationClaimID])\r\nREFERENCES [dbo].[OperationClaims] ([OperationClaimID])\r\n","GO\r\n","ALTER TABLE [dbo].[UserOperationClaims] CHECK CONSTRAINT [FK_UserOperationClaims_OperationClaims]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/ForeignKey[@Name='FK_UserOperationClaims_OperationClaims']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_UserOperationClaims_Users1]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/ForeignKey[@Name='FK_UserOperationClaims_Users1']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[UserOperationClaims]  WITH CHECK ADD  CONSTRAINT [FK_UserOperationClaims_Users1] FOREIGN KEY([UserID])\r\nREFERENCES [dbo].[Users] ([UserID])\r\n","GO\r\n","ALTER TABLE [dbo].[UserOperationClaims] CHECK CONSTRAINT [FK_UserOperationClaims_Users1]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='UserOperationClaims' and @Schema='dbo']/ForeignKey[@Name='FK_UserOperationClaims_Users1']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_WorkoutProgramDays_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramDays_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramDays]  WITH CHECK ADD  CONSTRAINT [FK_WorkoutProgramDays_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramDays] CHECK CONSTRAINT [FK_WorkoutProgramDays_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramDays_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_WorkoutProgramDays_WorkoutProgramTemplates]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramDays_WorkoutProgramTemplates']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramDays]  WITH CHECK ADD  CONSTRAINT [FK_WorkoutProgramDays_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID])\r\nREFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID])\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramDays] CHECK CONSTRAINT [FK_WorkoutProgramDays_WorkoutProgramTemplates]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramDays_WorkoutProgramTemplates']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_WorkoutProgramExercises_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramExercises_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises]  WITH CHECK ADD  CONSTRAINT [FK_WorkoutProgramExercises_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramExercises] CHECK CONSTRAINT [FK_WorkoutProgramExercises_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramExercises_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_WorkoutProgramExercises_WorkoutProgramDays]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramExercises_WorkoutProgramDays']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises]  WITH CHECK ADD  CONSTRAINT [FK_WorkoutProgramExercises_WorkoutProgramDays] FOREIGN KEY([WorkoutProgramDayID])\r\nREFERENCES [dbo].[WorkoutProgramDays] ([WorkoutProgramDayID])\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramExercises] CHECK CONSTRAINT [FK_WorkoutProgramExercises_WorkoutProgramDays]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramExercises_WorkoutProgramDays']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_WorkoutProgramTemplates_Companies]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramTemplates_Companies']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramTemplates]  WITH CHECK ADD  CONSTRAINT [FK_WorkoutProgramTemplates_Companies] FOREIGN KEY([CompanyID])\r\nREFERENCES [dbo].[Companies] ([CompanyID])\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramTemplates] CHECK CONSTRAINT [FK_WorkoutProgramTemplates_Companies]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramTemplates' and @Schema='dbo']/ForeignKey[@Name='FK_WorkoutProgramTemplates_Companies']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [CK_MemberWorkoutPrograms_CreationDate]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Check[@Name='CK_MemberWorkoutPrograms_CreationDate']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms]  WITH CHECK ADD  CONSTRAINT [CK_MemberWorkoutPrograms_CreationDate] CHECK  (([CreationDate]<=dateadd(second,(5),getdate())))\r\n","GO\r\n","ALTER TABLE [dbo].[MemberWorkoutPrograms] CHECK CONSTRAINT [CK_MemberWorkoutPrograms_CreationDate]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Check[@Name='CK_MemberWorkoutPrograms_CreationDate']","object_type":"Check"}},{"cell_type":"markdown","source":["# [CK_MemberWorkoutPrograms_DateRange]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Check[@Name='CK_MemberWorkoutPrograms_DateRange']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[MemberWorkoutPrograms]  WITH CHECK ADD  CONSTRAINT [CK_MemberWorkoutPrograms_DateRange] CHECK  (([EndDate] IS NULL OR [StartDate]<=[EndDate]))\r\n","GO\r\n","ALTER TABLE [dbo].[MemberWorkoutPrograms] CHECK CONSTRAINT [CK_MemberWorkoutPrograms_DateRange]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='MemberWorkoutPrograms' and @Schema='dbo']/Check[@Name='CK_MemberWorkoutPrograms_DateRange']","object_type":"Check"}},{"cell_type":"markdown","source":["# [CK_WorkoutProgramDays_DayNumber]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramDays_DayNumber']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramDays]  WITH CHECK ADD  CONSTRAINT [CK_WorkoutProgramDays_DayNumber] CHECK  (([DayNumber]>=(1) AND [DayNumber]<=(7)))\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramDays] CHECK CONSTRAINT [CK_WorkoutProgramDays_DayNumber]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramDays' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramDays_DayNumber']","object_type":"Check"}},{"cell_type":"markdown","source":["# [CK_WorkoutProgramExercises_ExerciseType]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_ExerciseType']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises]  WITH CHECK ADD  CONSTRAINT [CK_WorkoutProgramExercises_ExerciseType] CHECK  (([ExerciseType]='Company' OR [ExerciseType]='System'))\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramExercises] CHECK CONSTRAINT [CK_WorkoutProgramExercises_ExerciseType]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_ExerciseType']","object_type":"Check"}},{"cell_type":"markdown","source":["# [CK_WorkoutProgramExercises_OrderIndex]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_OrderIndex']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises]  WITH CHECK ADD  CONSTRAINT [CK_WorkoutProgramExercises_OrderIndex] CHECK  (([OrderIndex]>(0)))\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramExercises] CHECK CONSTRAINT [CK_WorkoutProgramExercises_OrderIndex]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_OrderIndex']","object_type":"Check"}},{"cell_type":"markdown","source":["# [CK_WorkoutProgramExercises_Sets]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_Sets']","object_type":"Check"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[WorkoutProgramExercises]  WITH CHECK ADD  CONSTRAINT [CK_WorkoutProgramExercises_Sets] CHECK  (([Sets]>(0)))\r\n","GO\r\n","ALTER TABLE [dbo].[WorkoutProgramExercises] CHECK CONSTRAINT [CK_WorkoutProgramExercises_Sets]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/Table[@Name='WorkoutProgramExercises' and @Schema='dbo']/Check[@Name='CK_WorkoutProgramExercises_Sets']","object_type":"Check"}},{"cell_type":"markdown","source":["# [dbo].[UpdateMembershipStatus]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/StoredProcedure[@Name='UpdateMembershipStatus' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[UpdateMembershipStatus]    Script Date: 20.06.2025 07:53:53 ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE   PROCEDURE [dbo].[UpdateMembershipStatus]\r\nAS\r\nBEGIN\r\n    DECLARE @UpdatedCount INT;\r\n\r\n    UPDATE dbo.Memberships\r\n    SET IsActive = 0\r\n    WHERE EndDate < GETDATE() AND IsActive = 1;\r\n\r\n    SET @UpdatedCount = @@ROWCOUNT;\r\n\r\n    SELECT CONVERT(VARCHAR(20), GETDATE(), 120) AS LogDate, \r\n           @UpdatedCount AS UpdatedMemberships,\r\n           'Membership statuses updated successfully.' AS Message;\r\nEND\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']/StoredProcedure[@Name='UpdateMembershipStatus' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [GymProject]"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["USE [master]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER DATABASE [GymProject] SET  READ_WRITE \r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='W']/Database[@Name='GymProject']","object_type":"Database"}}]}