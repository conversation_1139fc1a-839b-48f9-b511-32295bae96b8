# 🏋️ Çok Branşlı Üyelik Sistemi ve Detaylı Silme Sistemi Kurulum Promptu

## 📋 GENEL SİSTEM AÇIKLAMASI

Bu prompt, spor salonu yönetim sisteminde çok branşlı üyelik sistemi ve akıllı silme sistemini kurmak için tasarlanmıştır. Sistem 100+ spor salonu için optimize edilmiş, çok kiracılı (multi-tenant) mimariye sahiptir.

## 🎯 KURULACAK SİSTEMİN ÖZELLİKLERİ

### 1. ÇOK BRANŞLI ÜYELİK SİSTEMİ
- **Aynı paket yenileme:** Mevcut satırı güncelle (EndDate uzat)
- **Farklı paket/branş:** Yeni satır oluştur
- **Panelde gösterim:** Tüm aktif branşları birleştir (örn: "Fitness(40), Crossfit(60)")
- **Filtreleme:** <PERSON><PERSON><PERSON> ve paket türü bazında filtreleme

### 2. DETAYLI SİLME SİSTEMİ
- Üyenin tüm aktif üyeliklerini listele
- Kullanıcı hangi üyeliği silmek istediğini seçsin
- Ödeme bilgileri ve uyarıları göster
- Seçilen üyelik ve bağlı ödemeleri sil

### 3. PERFORMANS OPTİMİZASYONU
- Kritik database indexleri
- Connection pool optimizasyonu
- Cache sistemi güçlendirme
- Sorgu optimizasyonları

## 🔧 BACKEND KURULUM ADIMLARI

### ADIM 1: DTO Oluşturma
Dosya: `GymProjectBackend/Entities/DTOs/MemberActiveMembershipsDto.cs`
```csharp
using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class MemberActiveMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public List<ActiveMembershipDetailDto> ActiveMemberships { get; set; }
    }

    public class ActiveMembershipDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public int PaymentCount { get; set; }
        public DateTime CreationDate { get; set; }
        public bool CanBeDeleted { get; set; }
        public string DeleteWarning { get; set; }
    }
}
```

### ADIM 2: MembershipManager'a Metod Ekleme
Dosya: `GymProjectBackend/Business/Concrete/MembershipManager.cs`

Eklenecek metod:
```csharp
[SecuredOperation("owner,admin")]
[PerformanceAspect(3)]
public IDataResult<MemberActiveMembershipsDto> GetMemberActiveMemberships(int memberId)
{
    using (var context = new GymContext())
    {
        var companyId = _companyContext.GetCompanyId();
        var now = DateTime.Now;

        // Üye bilgisini al
        var member = context.Members.FirstOrDefault(m => m.MemberID == memberId && m.CompanyID == companyId);
        if (member == null)
        {
            return new ErrorDataResult<MemberActiveMembershipsDto>("Üye bulunamadı.");
        }

        // Aktif üyelikleri al
        var activeMemberships = from ms in context.Memberships
                               join mt in context.MembershipTypes on ms.MembershipTypeID equals mt.MembershipTypeID
                               where ms.MemberID == memberId 
                               && ms.IsActive == true 
                               && ms.EndDate > now
                               && ms.CompanyID == companyId
                               && mt.CompanyID == companyId
                               select new
                               {
                                   ms.MembershipID,
                                   ms.MembershipTypeID,
                                   mt.Branch,
                                   mt.TypeName,
                                   ms.StartDate,
                                   ms.EndDate,
                                   ms.CreationDate
                               };

        var membershipDetails = new List<ActiveMembershipDetailDto>();

        foreach (var membership in activeMemberships)
        {
            // Bu üyeliğe ait ödemeleri al
            var payments = context.Payments
                .Where(p => p.MemberShipID == membership.MembershipID && p.IsActive == true)
                .ToList();

            var remainingDays = (int)Math.Ceiling((membership.EndDate - now).TotalDays);
            var totalPaidAmount = payments.Sum(p => p.PaymentAmount);

            // Silme kontrolü
            var canBeDeleted = true;
            var deleteWarning = "";

            // Eğer birden fazla ödeme varsa uyarı ver
            if (payments.Count > 1)
            {
                deleteWarning = $"Bu üyeliğe {payments.Count} adet ödeme yapılmış. Tüm ödemeler silinecek.";
            }

            // Eğer borç ödemesi varsa uyarı ver
            var hasDebtPayments = context.RemainingDebts
                .Any(rd => payments.Select(p => p.PaymentID).Contains(rd.PaymentID) && rd.IsActive == true);
            
            if (hasDebtPayments)
            {
                deleteWarning += " Borç ödemeleri de etkilenecek.";
            }

            membershipDetails.Add(new ActiveMembershipDetailDto
            {
                MembershipID = membership.MembershipID,
                MembershipTypeID = membership.MembershipTypeID,
                Branch = membership.Branch,
                TypeName = membership.TypeName,
                StartDate = membership.StartDate,
                EndDate = membership.EndDate,
                RemainingDays = remainingDays,
                TotalPaidAmount = totalPaidAmount,
                PaymentCount = payments.Count,
                CreationDate = membership.CreationDate ?? DateTime.Now,
                CanBeDeleted = canBeDeleted,
                DeleteWarning = deleteWarning
            });
        }

        var result = new MemberActiveMembershipsDto
        {
            MemberID = memberId,
            MemberName = member.Name,
            ActiveMemberships = membershipDetails.OrderBy(m => m.CreationDate).ToList()
        };

        return new SuccessDataResult<MemberActiveMembershipsDto>(result);
    }
}
```

### ADIM 3: Interface Güncelleme
Dosya: `GymProjectBackend/Business/Abstract/IMembershipService.cs`

Eklenecek satır:
```csharp
IDataResult<MemberActiveMembershipsDto> GetMemberActiveMemberships(int memberId);
```

### ADIM 4: Controller Endpoint Ekleme
Dosya: `GymProjectBackend/WebAPI/Controllers/MembershipController.cs`

Eklenecek metod:
```csharp
[HttpGet("getactivememberships/{memberId}")]
public IActionResult GetMemberActiveMemberships(int memberId)
{
    var result = _membershipService.GetMemberActiveMemberships(memberId);
    if (result.Success)
    {
        return Ok(result);
    }
    return BadRequest(result);
}
```

### ADIM 5: Performans Optimizasyonları

#### A) Database Indexleri
Dosya: `GymProjectBackend/Scripts/CriticalPerformanceIndexes.sql`
```sql
-- Payment tablosu indexleri
CREATE NONCLUSTERED INDEX [IX_Payment_CompanyID_IsActive_PaymentDate] 
ON [dbo].[Payments] ([CompanyID], [IsActive], [PaymentDate] DESC)
INCLUDE ([PaymentAmount], [PaymentMethod], [MemberShipID])

-- Membership tablosu indexleri
CREATE NONCLUSTERED INDEX [IX_Membership_MemberID_CompanyID_IsActive_EndDate] 
ON [dbo].[Memberships] ([MemberID], [CompanyID], [IsActive], [EndDate] DESC)
INCLUDE ([MembershipTypeID], [StartDate], [MembershipID])

-- Member tablosu indexleri
CREATE NONCLUSTERED INDEX [IX_Member_CompanyID_IsActive] 
ON [dbo].[Members] ([CompanyID], [IsActive])
INCLUDE ([Name], [PhoneNumber], [Gender], [MemberID])
WHERE [IsActive] = 1

-- MembershipType tablosu indexleri
CREATE NONCLUSTERED INDEX [IX_MembershipType_CompanyID_IsActive] 
ON [dbo].[MembershipTypes] ([CompanyID], [IsActive])
INCLUDE ([Branch], [TypeName], [Price], [Day])
WHERE [IsActive] = 1
```

#### B) Connection Pool Optimizasyonu
Dosya: `GymProjectBackend/DataAccess/Concrete/EntityFramework/GymContext.cs`
```csharp
protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
{
    var connectionString = @"Server=localhost;Database=GymProject;Trusted_Connection=true;Encrypt=False;" +
                         "Max Pool Size=200;" +
                         "Min Pool Size=10;" +
                         "Connection Timeout=30;" +
                         "Command Timeout=60;" +
                         "Pooling=true;" +
                         "MultipleActiveResultSets=true;";
    
    optionsBuilder.UseSqlServer(connectionString);
    optionsBuilder.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
}
```

#### C) Cache Optimizasyonu
Dosya: `GymProjectBackend/Core/CrossCuttingConcerns/Caching/Configuration/CacheConfiguration.cs`
```csharp
// Payment cache ayarları
EntitySettings["Payment"] = new EntityCacheSettings
{
    DefaultDuration = 300, // 5 dakika
    Tags = new[] { "Payment", "Financial" },
    InvalidateOnEntityChange = true,
    RelatedEntities = new[] { "Member", "Transaction" }
};

// Member cache ayarları
EntitySettings["Member"] = new EntityCacheSettings
{
    DefaultDuration = 600, // 10 dakika
    Tags = new[] { "Member", "ActiveMembers" },
    InvalidateOnEntityChange = true,
    RelatedEntities = new[] { "Membership", "Payment" }
};
```

## 🎨 FRONTEND KURULUM ADIMLARI

### ADIM 1: Model Oluşturma
Dosya: `GymProjectFrontend/src/app/models/memberActiveMemberships.ts`
```typescript
export interface MemberActiveMemberships {
  memberID: number;
  memberName: string;
  activeMemberships: ActiveMembershipDetail[];
}

export interface ActiveMembershipDetail {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  totalPaidAmount: number;
  paymentCount: number;
  creationDate: Date;
  canBeDeleted: boolean;
  deleteWarning: string;
}
```

### ADIM 2: Servis Metodu Ekleme
Dosya: `GymProjectFrontend/src/app/services/membership.service.ts`
```typescript
import { MemberActiveMemberships } from '../models/memberActiveMemberships';
import { SingleResponseModel } from '../models/singleResponseModel';

getMemberActiveMemberships(memberId: number): Observable<SingleResponseModel<MemberActiveMemberships>> {
  return this.httpClient.get<SingleResponseModel<MemberActiveMemberships>>(
    `${this.apiUrl}membership/getactivememberships/${memberId}`
  );
}
```

### ADIM 3: Detaylı Silme Dialog Komponenti
Dosya: `GymProjectFrontend/src/app/components/membership-delete-dialog/membership-delete-dialog.component.ts`
```typescript
import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MembershipService } from '../../services/membership.service';
import { ToastrService } from 'ngx-toastr';
import { MemberActiveMemberships, ActiveMembershipDetail } from '../../models/memberActiveMemberships';

@Component({
  selector: 'app-membership-delete-dialog',
  templateUrl: './membership-delete-dialog.component.html',
  styleUrls: ['./membership-delete-dialog.component.css'],
  standalone: false
})
export class MembershipDeleteDialogComponent implements OnInit {
  memberActiveMemberships: MemberActiveMemberships | null = null;
  selectedMembershipId: number | null = null;
  isLoading = true;

  constructor(
    public dialogRef: MatDialogRef<MembershipDeleteDialogComponent>,
    @Inject(MAT_DIALOG_DATA) public data: { memberId: number, memberName: string },
    private membershipService: MembershipService,
    private toastr: ToastrService
  ) {}

  ngOnInit(): void {
    this.loadMemberActiveMemberships();
  }

  loadMemberActiveMemberships(): void {
    this.membershipService.getMemberActiveMemberships(this.data.memberId).subscribe({
      next: (response) => {
        if (response.success) {
          this.memberActiveMemberships = response.data;
          if (this.memberActiveMemberships.activeMemberships.length === 1) {
            this.selectedMembershipId = this.memberActiveMemberships.activeMemberships[0].membershipID;
          }
        } else {
          this.toastr.error('Aktif üyelikler yüklenemedi', 'Hata');
        }
        this.isLoading = false;
      },
      error: (error) => {
        this.toastr.error('Aktif üyelikler yüklenirken hata oluştu', 'Hata');
        this.isLoading = false;
      }
    });
  }

  selectMembership(membershipId: number): void {
    this.selectedMembershipId = membershipId;
  }

  onConfirm(): void {
    if (!this.selectedMembershipId) {
      this.toastr.warning('Lütfen silinecek üyeliği seçin', 'Uyarı');
      return;
    }
    this.dialogRef.close({ confirmed: true, membershipId: this.selectedMembershipId });
  }

  onCancel(): void {
    this.dialogRef.close({ confirmed: false });
  }
}
```

### ADIM 4: Dialog HTML Template
Dosya: `GymProjectFrontend/src/app/components/membership-delete-dialog/membership-delete-dialog.component.html`
```html
<div class="dialog-container">
  <div class="dialog-header">
    <h2 class="dialog-title">
      <i class="fas fa-trash-alt text-danger me-2"></i>
      Üyelik Silme İşlemi
    </h2>
  </div>

  <div class="dialog-content">
    <div class="member-info mb-4">
      <h4 class="text-primary">{{ data.memberName }}</h4>
      <p class="text-muted">Bu üyenin hangi üyeliğini silmek istiyorsunuz?</p>
    </div>

    <div *ngIf="isLoading" class="text-center">
      <div class="spinner-border text-primary" role="status"></div>
    </div>

    <div *ngIf="!isLoading && memberActiveMemberships" class="memberships-list">
      <div *ngFor="let membership of memberActiveMemberships.activeMemberships" 
           class="membership-card" 
           [class.selected]="selectedMembershipId === membership.membershipID"
           (click)="selectMembership(membership.membershipID)">
        
        <div class="membership-header">
          <div class="membership-title">
            <input type="radio" 
                   [value]="membership.membershipID" 
                   [checked]="selectedMembershipId === membership.membershipID"
                   (change)="selectMembership(membership.membershipID)"
                   class="form-check-input me-2">
            <strong>{{ membership.branch }} - {{ membership.typeName }}</strong>
          </div>
          <div class="remaining-days">
            <span class="badge bg-success">{{ membership.remainingDays }} gün kaldı</span>
          </div>
        </div>

        <div class="membership-details">
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">Toplam Ödeme:</small>
              <div class="fw-bold text-success">{{ membership.totalPaidAmount | currency:'TRY':'symbol':'1.2-2':'tr' }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Ödeme Sayısı:</small>
              <div>{{ membership.paymentCount }} adet</div>
            </div>
          </div>

          <div *ngIf="membership.deleteWarning" class="alert alert-warning mt-2 mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ membership.deleteWarning }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="dialog-footer">
    <button type="button" class="btn btn-secondary me-2" (click)="onCancel()">
      <i class="fas fa-times me-1"></i>İptal
    </button>
    <button type="button" class="btn btn-danger" [disabled]="!selectedMembershipId" (click)="onConfirm()">
      <i class="fas fa-trash-alt me-1"></i>Üyeliği Sil
    </button>
  </div>
</div>
```

### ADIM 5: Member Filter Component Güncelleme
Dosya: `GymProjectFrontend/src/app/components/member-filter/member-filter.component.ts`

Silme metodunu güncelle:
```typescript
import { MembershipDeleteDialogComponent } from '../membership-delete-dialog/membership-delete-dialog.component';

deleteMember(member: MemberFilter) {
  const dialogRef = this.dialog.open(MembershipDeleteDialogComponent, {
    width: '600px',
    data: { memberId: member.memberID, memberName: member.name }
  });

  dialogRef.afterClosed().subscribe(result => {
    if (result && result.confirmed) {
      this.isLoading = true;
      this.membershipService.delete(result.membershipId).subscribe({
        next: (response) => {
          this.isLoading = false;
          if (response.success) {
            this.toastrService.success(response.message, 'Başarılı');
            this.loadMembers();
            this.getTotalActiveMembers();
          } else {
            this.toastrService.error(response.message, 'Hata');
          }
        },
        error: (error) => {
          this.isLoading = false;
          this.toastrService.error('Üyelik silinirken bir hata oluştu.', 'Hata');
        }
      });
    }
  });
}
```

## 📋 KURULUM KONTROL LİSTESİ

### Backend Kontrolleri:
- [ ] MemberActiveMembershipsDto.cs oluşturuldu
- [ ] MembershipManager'a GetMemberActiveMemberships metodu eklendi
- [ ] IMembershipService interface'i güncellendi
- [ ] MembershipController'a endpoint eklendi
- [ ] Database indexleri çalıştırıldı
- [ ] GymContext connection string güncellendi
- [ ] Cache ayarları güncellendi

### Frontend Kontrolleri:
- [ ] memberActiveMemberships.ts modeli oluşturuldu
- [ ] MembershipService'e metod eklendi
- [ ] MembershipDeleteDialogComponent oluşturuldu
- [ ] Dialog HTML template oluşturuldu
- [ ] Dialog CSS stilleri eklendi
- [ ] MemberFilterComponent güncellendi
- [ ] AppModule'e dialog component eklendi

### Test Senaryoları:
- [ ] Tek üyeliği olan üye silme testi
- [ ] Çoklu üyeliği olan üye silme testi
- [ ] Borç ödemesi olan üyelik silme testi
- [ ] Filtreleme sistemi testi
- [ ] Performans testi (100+ salon simülasyonu)

## 🚀 BAŞLATMA TALİMATLARI

1. **Database Migration:** CriticalPerformanceIndexes.sql script'ini çalıştır
2. **Backend:** Tüm backend değişikliklerini uygula ve derle
3. **Frontend:** Tüm frontend değişikliklerini uygula ve derle
4. **Test:** Sistem testlerini çalıştır
5. **Monitoring:** Performans izleme araçlarını aktif et

## ⚠️ ÖNEMLİ NOTLAR

- Bu sistem 100+ spor salonu için optimize edilmiştir
- Database indexleri MUTLAKA uygulanmalıdır
- Cache ayarları production ortamında kritiktir
- Tüm değişiklikler mevcut kod tabanına uyumlu olarak tasarlanmıştır
- Multi-tenant mimari korunmuştur
- Güvenlik kontrolleri (SecuredOperation) korunmuştur

Bu prompt'u kullanarak sistemi tamamen kurabilirsiniz. Her adım detaylı olarak açıklanmış ve kod örnekleri verilmiştir.

## 🎯 PROMPT KULLANIM TALİMATI

Bu prompt'u AI'ya verirken şu şekilde kullanın:

"Aşağıdaki prompt'ta belirtilen çok branşlı üyelik sistemi ve detaylı silme sistemini tamamen kur. Tüm backend ve frontend kodlarını oluştur, database optimizasyonlarını uygula ve sistem testlerini hazırla. Mevcut kod tabanına uyumlu olarak çalış ve hiçbir adımı atlama."

## 📊 BEKLENEN SONUÇLAR

Sistem kurulduktan sonra:
- ✅ Üyeler birden fazla branşa üye olabilir
- ✅ Aynı paket yenileme mevcut satırı günceller
- ✅ Farklı paket/branş yeni satır oluşturur
- ✅ Panelde "Fitness(40), Crossfit(60)" şeklinde gösterim
- ✅ Detaylı silme dialog'u ile güvenli silme
- ✅ 100+ salon için optimize edilmiş performans
- ✅ Çok kiracılı mimari korunur
- ✅ Güvenlik kontrolleri aktif kalır

## 🔄 SİSTEM ÇALIŞMA MANTIKLARI

### Üyelik Ekleme Mantığı:
```
IF (aynı MembershipTypeID var) THEN
    Mevcut satırı güncelle (EndDate uzat)
ELSE
    Yeni satır oluştur
END IF
```

### Silme Mantığı:
```
1. Üyenin tüm aktif üyeliklerini listele
2. Kullanıcı seçim yapsın
3. Seçilen üyeliği ve bağlı ödemeleri sil
4. Diğer üyelikler etkilenmesin
```

### Filtreleme Mantığı:
```
- Branş filtresi: İlgili branşı olan tüm üyeler
- Paket türü filtresi: İlgili paketi olan tüm üyeler
- Arama: İsim ve telefon numarası
```

Bu sistem ile Türkiye'deki 10.000+ kişinin sorunsuz kullanabileceği, performanslı ve güvenilir bir spor salonu yönetim sistemi elde edeceksiniz.
