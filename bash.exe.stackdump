Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FE8E
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210286019, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF9DC0  000210068E24 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA0A0  00021006A225 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB90000000 ntdll.dll
7FFB8F3E0000 KERNEL32.DLL
7FFB8D1E0000 KERNELBASE.dll
7FFB8E890000 USER32.dll
7FFB8DB70000 win32u.dll
7FFB8FE50000 GDI32.dll
7FFB8D7F0000 gdi32full.dll
7FFB8DCF0000 msvcp_win.dll
7FFB8DBA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB8F4B0000 advapi32.dll
7FFB8F600000 msvcrt.dll
7FFB8DEC0000 sechost.dll
7FFB8DDA0000 RPCRT4.dll
7FFB8C870000 CRYPTBASE.DLL
7FFB8D750000 bcryptPrimitives.dll
7FFB8FF80000 IMM32.DLL
