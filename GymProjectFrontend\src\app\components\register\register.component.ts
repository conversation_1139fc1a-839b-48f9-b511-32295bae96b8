import { Component, OnInit, Inject, PLATFORM_ID } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AuthService } from '../../services/auth.service';
import { RateLimitService } from '../../services/rate-limit.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';
import { finalize } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrl: './register.component.css',
  standalone: false,
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  isLoading: boolean = false;
  passwordVisible: boolean = false;
  confirmPasswordVisible: boolean = false;
  isBanned: boolean = false;
  remainingBanTime: string = '';
  private isBrowser: boolean;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private rateLimitService: RateLimitService,
    private toastrService: ToastrService,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.isBrowser = isPlatformBrowser(this.platformId);
  }

  ngOnInit(): void {
    this.createRegisterForm();
    if (this.isBrowser) {
      this.checkRegisterBanStatus();
    }
  }

  createRegisterForm() {
    this.registerForm = this.formBuilder.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required]
    }, {
      validator: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(g: FormGroup) {
    const password = g.get('password')?.value;
    const confirmPassword = g.get('confirmPassword')?.value;
    return password === confirmPassword ? null : { 'mismatch': true };
  }

  checkRegisterBanStatus() {
    if (!this.isBrowser) return;

    this.rateLimitService.checkRegisterBan().subscribe({
      next: (response) => {
        if (response.success && response.isBanned) {
          this.isBanned = true;
          this.remainingBanTime = this.rateLimitService.formatRemainingTime(response.remainingMinutes);
          this.toastrService.error(`Çok fazla kayıt denemesi. ${this.remainingBanTime} sonra tekrar deneyin.`, 'Kayıt Engellendi');
        } else {
          this.isBanned = false;
          this.remainingBanTime = '';
        }
      },
      error: (error) => {
        console.error('Register ban status check failed:', error);
      }
    });
  }

  register() {
    if (this.isBanned) {
      this.toastrService.error(`Kayıt engellenmiş. ${this.remainingBanTime} sonra tekrar deneyin.`, 'Kayıt Engellendi');
      return;
    }
    if (!this.registerForm.valid) {
      this.toastrService.error('Lütfen tüm alanları doğru şekilde doldurun');
      return;
    }

    this.isLoading = true;
    const registerModel = {
      firstName: this.registerForm.value.firstName,
      lastName: this.registerForm.value.lastName,
      email: this.registerForm.value.email,
      password: this.registerForm.value.password
    };

    this.authService.registerMember(registerModel)
      .pipe(finalize(() => this.isLoading = false))
      .subscribe({
        next: (response) => {
          if (response.success) {
            this.toastrService.success('Kayıt başarılı! Giriş yapabilirsiniz.', 'Başarılı');
            this.router.navigate(['/login']);
          } else {
            this.toastrService.error(response.message || 'Kayıt işlemi başarısız');
          }
        },
        error: (error) => {
          this.toastrService.error(error.message || 'Kayıt işlemi sırasında bir hata oluştu');

          // Başarısız kayıt sonrası ban durumunu tekrar kontrol et
          setTimeout(() => {
            this.checkRegisterBanStatus();
          }, 1000);
        }
      });
  }

  // Şifre görünürlüğünü değiştirir
  togglePasswordVisibility(): void {
    this.passwordVisible = !this.passwordVisible;
  }

  // Şifre tekrarı görünürlüğünü değiştirir
  toggleConfirmPasswordVisibility(): void {
    this.confirmPasswordVisible = !this.confirmPasswordVisible;
  }
}