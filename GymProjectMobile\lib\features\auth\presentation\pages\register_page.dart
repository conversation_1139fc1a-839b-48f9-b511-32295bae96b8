/// Register Page - GymKod Pro Mobile
///
/// Bu sayfa Angular frontend'deki register component'inden uyarlanmıştır.
/// Referans: Angular register component tasarımı
///
/// RESPONSIVE DESIGN ENHANCEMENT:
/// Bu sayfa artık responsive tasarım desteği içerir.
/// Angular frontend'deki responsive register pattern'leri u<PERSON>tır.
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/widgets.dart';
import '../providers/auth_provider.dart';

/// Register Page
/// Angular frontend'deki register component'e benzer
/// isMemberRegistration: true ise member kayıt, false ise admin kayıt
class RegisterPage extends ConsumerStatefulWidget {
  final bool isMemberRegistration;

  const RegisterPage({
    super.key,
    this.isMemberRegistration = false,
  });

  @override
  ConsumerState<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends ConsumerState<RegisterPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();

  final _firstNameFocusNode = FocusNode();
  final _lastNameFocusNode = FocusNode();
  final _emailFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();
  final _confirmPasswordFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    // Animation setup (Angular'daki page transition'a benzer)
    _animationController = AnimationController(
      duration: AppConstants.normalAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();

    LoggingService.widgetLog('RegisterPage', 'initState',
      details: 'isMemberRegistration: ${widget.isMemberRegistration}');
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _phoneController.dispose();

    _firstNameFocusNode.dispose();
    _lastNameFocusNode.dispose();
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _confirmPasswordFocusNode.dispose();
    _phoneFocusNode.dispose();

    _animationController.dispose();
    super.dispose();
  }

  /// Form validation
  bool _validateForm() {
    return _formKey.currentState?.validate() ?? false;
  }

  /// Register işlemi
  Future<void> _handleRegister() async {
    if (!_validateForm()) return;

    final firstName = _firstNameController.text.trim();
    final lastName = _lastNameController.text.trim();
    final email = _emailController.text.trim();
    final password = _passwordController.text;
    final phone = _phoneController.text.trim();

    LoggingService.authLog(
      widget.isMemberRegistration ? 'Member register attempt' : 'Admin register attempt',
      details: 'Email: $email',
    );

    bool success;

    if (widget.isMemberRegistration) {
      // Member registration
      success = await ref.read(authProvider.notifier).registerMember(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
        phoneNumber: phone,
      );
    } else {
      // Admin registration
      success = await ref.read(authProvider.notifier).register(
        firstName: firstName,
        lastName: lastName,
        email: email,
        password: password,
      );
    }

    if (success && mounted) {
      LoggingService.navigationLog('Register success redirect', '/home');
      context.go('/home');
    }
  }

  /// Login sayfasına git
  void _goToLogin() {
    LoggingService.navigationLog('Navigate to login', '/auth/login');
    context.go('/auth/login');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
          onPressed: () => context.pop(),
        ),
      ),
      extendBodyBehindAppBar: true,
      body: Container(
        decoration: BoxDecoration(
          // Angular register background gradient'ı
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: theme.brightness == Brightness.light
                ? AppColors.backgroundGradient
                : AppColors.darkBackgroundGradient,
          ),
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: ResponsiveContainer(
                    maxWidth: 400,
                    child: SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Header (Angular register-header'a benzer)
                          _buildHeader(theme),

                          const ResponsiveSpacing.vertical(
                            mobile: 24.0,
                            tablet: 28.0,
                            desktop: 32.0,
                          ),

                          // Register Form Card (Angular register-form'a benzer)
                          _buildRegisterForm(theme, authState),

                          const ResponsiveSpacing.vertical(
                            mobile: 20.0,
                            tablet: 22.0,
                            desktop: 24.0,
                          ),

                          // Login Link (Angular login-link'e benzer)
                          _buildLoginLink(theme),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  /// Header (Logo + Title)
  Widget _buildHeader(ThemeData theme) {
    return Column(
      children: [
        // Logo (Angular register-logo'ya benzer) - Responsive
        Container(
          width: AppSpacing.responsive(
            context,
            mobile: 70.0,
            tablet: 75.0,
            desktop: 80.0,
          ),
          height: AppSpacing.responsive(
            context,
            mobile: 70.0,
            tablet: 75.0,
            desktop: 80.0,
          ),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: widget.isMemberRegistration
                  ? AppColors.secondaryGradient
                  : AppColors.primaryGradient,
            ),
            boxShadow: [
              BoxShadow(
                color: (widget.isMemberRegistration
                    ? theme.colorScheme.secondary
                    : theme.colorScheme.primary).withValues(alpha: 0.3),
                blurRadius: AppSpacing.responsive(
                  context,
                  mobile: 12.0,
                  tablet: 13.0,
                  desktop: 15.0,
                ),
                offset: Offset(0, AppSpacing.responsive(
                  context,
                  mobile: 6.0,
                  tablet: 7.0,
                  desktop: 8.0,
                )),
              ),
            ],
          ),
          child: Icon(
            widget.isMemberRegistration ? Icons.person_add : Icons.admin_panel_settings,
            size: AppSpacing.responsiveIconSize(
              context,
              mobile: 35.0,
              tablet: 37.0,
              desktop: 40.0,
            ),
            color: theme.colorScheme.onPrimary,
          ),
        ),

        const ResponsiveSpacing.vertical(
          mobile: 20.0,
          tablet: 22.0,
          desktop: 24.0,
        ),

        // Title (Angular register-title'a benzer) - Responsive
        ResponsiveText(
          widget.isMemberRegistration ? 'Üye Ol' : 'Admin Kayıt',
          textType: 'h2',
          style: TextStyle(
            color: theme.colorScheme.onSurface,
          ),
        ),

        const ResponsiveSpacing.vertical(
          mobile: 6.0,
          tablet: 7.0,
          desktop: 8.0,
        ),

        // Subtitle - Responsive
        ResponsiveText(
          widget.isMemberRegistration
              ? 'Yeni üye hesabı oluşturun'
              : 'Yeni admin hesabı oluşturun',
          textType: 'bodylarge',
          style: TextStyle(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  /// Register Form - Responsive
  Widget _buildRegisterForm(ThemeData theme, AuthState authState) {
    return Card(
      elevation: AppSpacing.responsiveElevation(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          AppSpacing.responsiveBorderRadius(context),
        ),
      ),
      child: Padding(
        padding: AppSpacing.responsiveCardPadding(context),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Name Fields Row (Angular name-fields'e benzer) - Responsive
              ResponsiveBuilder(
                builder: (context, deviceType) {
                  if (deviceType == DeviceType.mobile) {
                    // Mobile'da dikey layout
                    return Column(
                      children: [
                        // First Name
                        CustomTextField(
                          label: 'Ad',
                          hintText: 'Adınız',
                          controller: _firstNameController,
                          focusNode: _firstNameFocusNode,
                          textInputAction: TextInputAction.next,
                          autofocus: true,
                          enabled: !authState.isLoading,
                          prefixIcon: Icons.person_outline,
                          textCapitalization: TextCapitalization.words,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Ad gerekli';
                            }
                            if (value.length < 2) {
                              return 'Ad en az 2 karakter olmalı';
                            }
                            return null;
                          },
                          onSubmitted: (_) => _lastNameFocusNode.requestFocus(),
                        ),

                        ResponsiveSpacing.vertical(
                          mobile: AppSpacing.responsiveFormFieldSpacing(context),
                          tablet: AppSpacing.responsiveFormFieldSpacing(context),
                          desktop: AppSpacing.responsiveFormFieldSpacing(context),
                        ),

                        // Last Name
                        CustomTextField(
                          label: 'Soyad',
                          hintText: 'Soyadınız',
                          controller: _lastNameController,
                          focusNode: _lastNameFocusNode,
                          textInputAction: TextInputAction.next,
                          enabled: !authState.isLoading,
                          prefixIcon: Icons.person_outline,
                          textCapitalization: TextCapitalization.words,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Soyad gerekli';
                            }
                            if (value.length < 2) {
                              return 'Soyad en az 2 karakter olmalı';
                            }
                            return null;
                          },
                          onSubmitted: (_) => _emailFocusNode.requestFocus(),
                        ),
                      ],
                    );
                  } else {
                    // Tablet/Desktop'ta yatay layout
                    return Row(
                      children: [
                        // First Name
                        Expanded(
                          child: CustomTextField(
                            label: 'Ad',
                            hintText: 'Adınız',
                            controller: _firstNameController,
                            focusNode: _firstNameFocusNode,
                            textInputAction: TextInputAction.next,
                            autofocus: true,
                            enabled: !authState.isLoading,
                            prefixIcon: Icons.person_outline,
                            textCapitalization: TextCapitalization.words,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Ad gerekli';
                              }
                              if (value.length < 2) {
                                return 'Ad en az 2 karakter olmalı';
                              }
                              return null;
                            },
                            onSubmitted: (_) => _lastNameFocusNode.requestFocus(),
                          ),
                        ),

                        SizedBox(width: AppSpacing.responsive(
                          context,
                          mobile: 12.0,
                          tablet: 14.0,
                          desktop: 16.0,
                        )),

                        // Last Name
                        Expanded(
                          child: CustomTextField(
                            label: 'Soyad',
                            hintText: 'Soyadınız',
                            controller: _lastNameController,
                            focusNode: _lastNameFocusNode,
                            textInputAction: TextInputAction.next,
                            enabled: !authState.isLoading,
                            prefixIcon: Icons.person_outline,
                            textCapitalization: TextCapitalization.words,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Soyad gerekli';
                              }
                              if (value.length < 2) {
                                return 'Soyad en az 2 karakter olmalı';
                              }
                              return null;
                            },
                            onSubmitted: (_) => _emailFocusNode.requestFocus(),
                          ),
                        ),
                      ],
                    );
                  }
                },
              ),

              ResponsiveSpacing.vertical(
                mobile: AppSpacing.responsiveFormFieldSpacing(context),
                tablet: AppSpacing.responsiveFormFieldSpacing(context),
                desktop: AppSpacing.responsiveFormFieldSpacing(context),
              ),

              // Email Field (Angular email input'a benzer)
              EmailTextField(
                label: 'E-posta',
                hintText: 'E-posta adresinizi girin',
                controller: _emailController,
                focusNode: _emailFocusNode,
                textInputAction: TextInputAction.next,
                enabled: !authState.isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppConstants.emailRequiredMessage;
                  }
                  if (!RegExp(AppConstants.emailPattern).hasMatch(value)) {
                    return AppConstants.emailInvalidMessage;
                  }
                  return null;
                },
                onSubmitted: (_) => widget.isMemberRegistration
                    ? _phoneFocusNode.requestFocus()
                    : _passwordFocusNode.requestFocus(),
              ),

              // Phone Field (sadece member registration için)
              if (widget.isMemberRegistration) ...[
                ResponsiveSpacing.vertical(
                  mobile: AppSpacing.responsiveFormFieldSpacing(context),
                  tablet: AppSpacing.responsiveFormFieldSpacing(context),
                  desktop: AppSpacing.responsiveFormFieldSpacing(context),
                ),

                PhoneTextField(
                  label: 'Telefon',
                  hintText: 'Telefon numaranızı girin',
                  controller: _phoneController,
                  focusNode: _phoneFocusNode,
                  textInputAction: TextInputAction.next,
                  enabled: !authState.isLoading,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Telefon numarası gerekli';
                    }
                    if (value.length < 10) {
                      return 'Geçerli bir telefon numarası girin';
                    }
                    return null;
                  },
                  onSubmitted: (_) => _passwordFocusNode.requestFocus(),
                ),
              ],

              ResponsiveSpacing.vertical(
                mobile: AppSpacing.responsiveFormFieldSpacing(context),
                tablet: AppSpacing.responsiveFormFieldSpacing(context),
                desktop: AppSpacing.responsiveFormFieldSpacing(context),
              ),

              // Password Field (Angular password input'a benzer)
              PasswordTextField(
                label: 'Şifre',
                hintText: 'Şifrenizi girin',
                controller: _passwordController,
                focusNode: _passwordFocusNode,
                textInputAction: TextInputAction.next,
                enabled: !authState.isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return AppConstants.passwordRequiredMessage;
                  }
                  if (value.length < AppConstants.minPasswordLength) {
                    return AppConstants.passwordTooShortMessage;
                  }
                  return null;
                },
                onSubmitted: (_) => _confirmPasswordFocusNode.requestFocus(),
              ),

              ResponsiveSpacing.vertical(
                mobile: AppSpacing.responsiveFormFieldSpacing(context),
                tablet: AppSpacing.responsiveFormFieldSpacing(context),
                desktop: AppSpacing.responsiveFormFieldSpacing(context),
              ),

              // Confirm Password Field
              PasswordTextField(
                label: 'Şifre Tekrar',
                hintText: 'Şifrenizi tekrar girin',
                controller: _confirmPasswordController,
                focusNode: _confirmPasswordFocusNode,
                textInputAction: TextInputAction.done,
                enabled: !authState.isLoading,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Şifre tekrarı gerekli';
                  }
                  if (value != _passwordController.text) {
                    return 'Şifreler eşleşmiyor';
                  }
                  return null;
                },
                onSubmitted: (_) => _handleRegister(),
              ),

              ResponsiveSpacing.vertical(
                mobile: 20.0,
                tablet: 22.0,
                desktop: 24.0,
              ),

              // Error Message (Angular error-message'a benzer) - Responsive
              if (authState.error != null) ...[
                Container(
                  padding: AppSpacing.responsivePadding(context),
                  decoration: BoxDecoration(
                    color: AppColors.dangerLight,
                    borderRadius: BorderRadius.circular(
                      AppSpacing.responsiveBorderRadius(context),
                    ),
                    border: Border.all(color: AppColors.danger),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: AppColors.danger,
                        size: AppSpacing.responsiveIconSize(context),
                      ),
                      SizedBox(width: AppSpacing.responsive(
                        context,
                        mobile: 6.0,
                        tablet: 7.0,
                        desktop: 8.0,
                      )),
                      Expanded(
                        child: ResponsiveText(
                          authState.error!,
                          textType: 'bodysmall',
                          style: const TextStyle(
                            color: AppColors.danger,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const ResponsiveSpacing.vertical(
                  mobile: 12.0,
                  tablet: 14.0,
                  desktop: 16.0,
                ),
              ],

              // Register Button (Angular register-button'a benzer)
              PrimaryButton(
                text: widget.isMemberRegistration ? 'Üye Ol' : 'Admin Kayıt',
                onPressed: authState.isLoading ? null : _handleRegister,
                isLoading: authState.isLoading,
                icon: widget.isMemberRegistration ? Icons.person_add : Icons.admin_panel_settings,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Login Link
  Widget _buildLoginLink(ThemeData theme) {
    return Column(
      children: [
        // Divider
        Row(
          children: [
            Expanded(
              child: Divider(
                color: theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
            Padding(
              padding: AppSpacing.responsivePadding(
                context,
                mobile: const EdgeInsets.symmetric(horizontal: 12.0),
                tablet: const EdgeInsets.symmetric(horizontal: 14.0),
                desktop: const EdgeInsets.symmetric(horizontal: 16.0),
              ),
              child: ResponsiveText(
                'Zaten hesabınız var mı?',
                textType: 'bodysmall',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
            Expanded(
              child: Divider(
                color: theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
          ],
        ),

        const ResponsiveSpacing.vertical(
          mobile: 20.0,
          tablet: 22.0,
          desktop: 24.0,
        ),

        // Login Button (Angular login-link'e benzer)
        OutlineButton(
          text: 'Giriş Yap',
          onPressed: _goToLogin,
          icon: Icons.login,
          isFullWidth: true,
        ),
      ],
    );
  }
}
