.dialog-container {
  max-width: 600px;
  width: 100%;
}

.dialog-header {
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.dialog-title {
  margin: 0;
  color: #dc3545;
  font-size: 1.25rem;
}

.dialog-content {
  max-height: 500px;
  overflow-y: auto;
}

.member-info h4 {
  margin-bottom: 0.5rem;
}

.memberships-list {
  max-height: 300px;
  overflow-y: auto;
}

.membership-card {
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #fff;
}

.membership-card:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.membership-card.selected {
  border-color: #007bff;
  background-color: #f8f9fa;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.25);
}

.membership-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.membership-title {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
}

.membership-details {
  font-size: 0.9rem;
}

.remaining-days .badge {
  font-size: 0.8rem;
}

.selected-info {
  border-left: 4px solid #dc3545;
}

.dialog-footer {
  border-top: 1px solid #dee2e6;
  padding-top: 1rem;
  margin-top: 1.5rem;
  display: flex;
  justify-content: flex-end;
}

.btn {
  min-width: 100px;
}

.alert {
  font-size: 0.85rem;
  padding: 0.5rem 0.75rem;
}

/* Dark mode support */
[data-bs-theme="dark"] .membership-card {
  background-color: var(--bs-dark);
  border-color: var(--bs-gray-600);
}

[data-bs-theme="dark"] .membership-card.selected {
  background-color: var(--bs-gray-800);
  border-color: var(--bs-primary);
}

[data-bs-theme="dark"] .selected-info {
  background-color: var(--bs-gray-800) !important;
}
