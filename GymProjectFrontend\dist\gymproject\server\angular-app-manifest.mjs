
export default {
  bootstrap: () => import('./main.server.mjs').then(m => m.default),
  inlineCriticalCss: true,
  baseHref: '/',
  locale: undefined,
  routes: undefined,
  entryPointToBrowserMapping: {},
  assets: {
    'index.csr.html': {size: 41548, hash: 'bc6ad2db6b67c60816c8cc78b8a985a8e81810ce8d57723c6aaefc4a453d6d94', text: () => import('./assets-chunks/index_csr_html.mjs').then(m => m.default)},
    'index.server.html': {size: 21716, hash: 'f121ae07f66a43e75579f39daddfd31738e89f41ac05a18fc7f6dfa642664c3f', text: () => import('./assets-chunks/index_server_html.mjs').then(m => m.default)},
    'styles-TBXNKKXU.css': {size: 298105, hash: '7i4JWBKmeRM', text: () => import('./assets-chunks/styles-TBXNKKXU_css.mjs').then(m => m.default)}
  },
};
