<div class="dialog-container">
  <div class="dialog-header">
    <h2 class="dialog-title">
      <i class="fas fa-trash-alt text-danger me-2"></i>
      Üyelik Silme İşlemi
    </h2>
  </div>

  <div class="dialog-content">
    <div class="member-info mb-4">
      <h4 class="text-primary">{{ data.memberName }}</h4>
      <p class="text-muted">Bu üyenin hangi üyeliğini silmek istiyorsunuz?</p>
    </div>

    <div *ngIf="isLoading" class="text-center">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Yükleniyor...</span>
      </div>
    </div>

    <div *ngIf="!isLoading && memberActiveMemberships" class="memberships-list">
      <div *ngIf="memberActiveMemberships.activeMemberships.length === 0" class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        Bu üyenin aktif üyeliği bulunmamaktadır.
      </div>

      <div *ngFor="let membership of memberActiveMemberships.activeMemberships" 
           class="membership-card" 
           [class.selected]="selectedMembershipId === membership.membershipID"
           (click)="selectMembership(membership.membershipID)">
        
        <div class="membership-header">
          <div class="membership-title">
            <input type="radio" 
                   [value]="membership.membershipID" 
                   [checked]="selectedMembershipId === membership.membershipID"
                   (change)="selectMembership(membership.membershipID)"
                   class="form-check-input me-2">
            <strong>{{ membership.branch }} - {{ membership.typeName }}</strong>
          </div>
          <div class="remaining-days">
            <span class="badge bg-success">{{ membership.remainingDays }} gün kaldı</span>
          </div>
        </div>

        <div class="membership-details">
          <div class="row">
            <div class="col-md-6">
              <small class="text-muted">Başlangıç:</small>
              <div>{{ formatDate(membership.startDate) }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Bitiş:</small>
              <div>{{ formatDate(membership.endDate) }}</div>
            </div>
          </div>
          
          <div class="row mt-2">
            <div class="col-md-6">
              <small class="text-muted">Toplam Ödeme:</small>
              <div class="fw-bold text-success">{{ formatCurrency(membership.totalPaidAmount) }}</div>
            </div>
            <div class="col-md-6">
              <small class="text-muted">Ödeme Sayısı:</small>
              <div>{{ membership.paymentCount }} adet</div>
            </div>
          </div>

          <div *ngIf="membership.deleteWarning" class="alert alert-warning mt-2 mb-0">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ membership.deleteWarning }}
          </div>
        </div>
      </div>
    </div>

    <div *ngIf="getSelectedMembership()" class="selected-info mt-4 p-3 bg-light rounded">
      <h6 class="text-danger">
        <i class="fas fa-exclamation-triangle me-2"></i>
        Silme Onayı
      </h6>
      <p class="mb-2">
        <strong>{{ getSelectedMembership()?.branch }} - {{ getSelectedMembership()?.typeName }}</strong> 
        üyeliği silinecek.
      </p>
      <p class="mb-0 text-muted">
        Bu işlem geri alınamaz. Üyeliğe ait tüm ödemeler de silinecektir.
      </p>
    </div>
  </div>

  <div class="dialog-footer">
    <button type="button" 
            class="btn btn-secondary me-2" 
            (click)="onCancel()">
      <i class="fas fa-times me-1"></i>
      İptal
    </button>
    <button type="button" 
            class="btn btn-danger" 
            [disabled]="!selectedMembershipId"
            (click)="onConfirm()">
      <i class="fas fa-trash-alt me-1"></i>
      Üyeliği Sil
    </button>
  </div>
</div>
