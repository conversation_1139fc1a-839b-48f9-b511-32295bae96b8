using System;
using System.Collections.Generic;

namespace Core.CrossCuttingConcerns.Caching.Configuration
{
    public class CacheConfiguration
    {
        public int DefaultDurationMinutes { get; set; } = 60;
        public int MaxCacheSize { get; set; } = 10000;
        public bool EnableStatistics { get; set; } = true;
        public bool EnableDebugLogging { get; set; } = false;
        
        // Tenant bazlı cache ayarları
        public Dictionary<string, EntityCacheSettings> EntitySettings { get; set; } = new();
        
        public CacheConfiguration()
        {
            InitializeDefaultEntitySettings();
        }
        
        private void InitializeDefaultEntitySettings()
        {
            // Member cache ayarları
            EntitySettings["Member"] = new EntityCacheSettings
            {
                DefaultDuration = 30,
                Tags = new[] { "Member", "User" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Membership", "Payment" }
            };
            
            // Product cache ayarları
            EntitySettings["Product"] = new EntityCacheSettings
            {
                DefaultDuration = 120,
                Tags = new[] { "Product", "Inventory" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Transaction" }
            };
            
            // Payment cache ayarları (100+ salon için optimize edildi)
            EntitySettings["Payment"] = new EntityCacheSettings
            {
                DefaultDuration = 300, // 5 dakika (60'dan artırıldı)
                Tags = new[] { "Payment", "Financial" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Transaction" }
            };

            // Membership cache ayarları (100+ salon için optimize edildi)
            EntitySettings["Membership"] = new EntityCacheSettings
            {
                DefaultDuration = 180, // 3 dakika (45'ten artırıldı)
                Tags = new[] { "Membership", "Member" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Member", "Payment" }
            };

            // Member cache ayarları (yeni eklendi - kritik!)
            EntitySettings["Member"] = new EntityCacheSettings
            {
                DefaultDuration = 600, // 10 dakika
                Tags = new[] { "Member", "ActiveMembers" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Membership", "Payment" }
            };

            // MembershipType cache ayarları (yeni eklendi - static data)
            EntitySettings["MembershipType"] = new EntityCacheSettings
            {
                DefaultDuration = 3600, // 1 saat (paket türleri sık değişmez)
                Tags = new[] { "MembershipType", "Static" },
                InvalidateOnEntityChange = true,
                RelatedEntities = new[] { "Membership" }
            };
            
            // City/Town cache ayarları (static data)
            EntitySettings["City"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "Town" }
            };
            
            EntitySettings["Town"] = new EntityCacheSettings
            {
                DefaultDuration = 1440, // 24 saat
                Tags = new[] { "Location", "Static" },
                InvalidateOnEntityChange = false,
                RelatedEntities = new[] { "City" }
            };
        }
    }
    
    public class EntityCacheSettings
    {
        public int DefaultDuration { get; set; }
        public string[] Tags { get; set; } = Array.Empty<string>();
        public bool InvalidateOnEntityChange { get; set; } = true;
        public string[] RelatedEntities { get; set; } = Array.Empty<string>();
    }
}
