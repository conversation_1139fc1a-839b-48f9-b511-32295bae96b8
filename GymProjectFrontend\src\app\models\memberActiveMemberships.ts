export interface MemberActiveMemberships {
  memberID: number;
  memberName: string;
  activeMemberships: ActiveMembershipDetail[];
}

export interface ActiveMembershipDetail {
  membershipID: number;
  membershipTypeID: number;
  branch: string;
  typeName: string;
  startDate: Date;
  endDate: Date;
  remainingDays: number;
  totalPaidAmount: number;
  paymentCount: number;
  creationDate: Date;
  canBeDeleted: boolean;
  deleteWarning: string;
}
