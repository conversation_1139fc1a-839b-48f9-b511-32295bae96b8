using Core.Entities;
using System;
using System.Collections.Generic;

namespace Entities.DTOs
{
    public class MemberActiveMembershipsDto : IDto
    {
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public List<ActiveMembershipDetailDto> ActiveMemberships { get; set; }
    }

    public class ActiveMembershipDetailDto : IDto
    {
        public int MembershipID { get; set; }
        public int MembershipTypeID { get; set; }
        public string Branch { get; set; }
        public string TypeName { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime EndDate { get; set; }
        public int RemainingDays { get; set; }
        public decimal TotalPaidAmount { get; set; }
        public int PaymentCount { get; set; }
        public DateTime CreationDate { get; set; }
        public bool CanBeDeleted { get; set; } // Silinebilir mi kontrolü
        public string DeleteWarning { get; set; } // Silme uyarı mesajı
    }
}
